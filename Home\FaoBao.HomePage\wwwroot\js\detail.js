﻿/**
 * Document Center JavaScript
 * 文档中心交互功能
 */

document.addEventListener('DOMContentLoaded', function() {
    initDocumentCenter();
});

function initDocumentCenter() {
    initSidebarToggle();
    initCategorySelector();
    initNavigation();
    initHierarchicalNavigation();
    initExpandCollapse();
    initTableOfContents();
    initVersionSelector();
    initHomepage();
    generateTableOfContents();
    calculateReadingTime();
}

// 侧边栏切换功能
function initSidebarToggle() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const openSidebarBtn = document.getElementById('openSidebar');
    const sidebar = document.querySelector('.doc-sidebar');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
    }
    
    if (openSidebarBtn) {
        openSidebarBtn.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
    }
    
    // 点击外部关闭侧边栏（移动端）
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target) && 
                !openSidebarBtn.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        }
    });
}

// 分类选择器功能
function initCategorySelector() {
    const categorySelect = document.getElementById('categorySelect');

    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            const selectedBook = this.value;

            if (selectedBook) {
                // 跳转到带有 book 参数的页面
                window.location.href = `/Document/${encodeURIComponent(selectedBook)}`;
            } else {
                // 如果没有选择分类，跳转到默认页面
                window.location.href = '/Document';
            }
        });
    }
}

// 导航功能
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // 如果是有效的文档链接，允许正常跳转
            if (href && href !== '#') {
                // 让浏览器正常处理链接跳转
                return;
            }

            // 对于没有有效链接的项目，阻止默认行为
            e.preventDefault();

            // 移除所有活动状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加当前活动状态
            this.parentElement.classList.add('active');


        });
    });
}

// 层级导航功能
function initHierarchicalNavigation() {
    // 初始化展开/折叠功能
    initExpandCollapse();

    // 设置初始状态
    setInitialExpandState();

    // 绑定导航点击事件
    bindNavigationEvents();

    // 初始化目录导航
    initDirectoriesNavigation();
}

// 展开/折叠功能
function initExpandCollapse() {
    // 处理标题的展开/折叠
    const sectionTitles = document.querySelectorAll('.nav-section-title');
    sectionTitles.forEach(title => {
        const expandIcon = title.querySelector('.expand-icon');
        if (expandIcon) {
            title.addEventListener('click', function(e) {
                e.preventDefault();
                toggleSection(this);
            });
        }
    });

    // 处理导航项的展开/折叠
    const expandIcons = document.querySelectorAll('.nav-item .expand-icon');
    expandIcons.forEach(icon => {
        icon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const navItem = this.closest('.nav-item');
            if (navItem) {
                toggleNavItem(navItem);
            }
        });
    });
}

// 切换区域展开/折叠状态
function toggleSection(sectionTitle) {
    const section = sectionTitle.parentElement;
    const children = section.querySelector('.nav-children');
    const expandIcon = sectionTitle.querySelector('.expand-icon');

    if (children) {
        const isExpanded = sectionTitle.classList.contains('expanded');

        if (isExpanded) {
            // 折叠
            sectionTitle.classList.remove('expanded');
            children.classList.remove('expanded');
            children.classList.add('collapsed');
        } else {
            // 展开
            sectionTitle.classList.add('expanded');
            children.classList.remove('collapsed');
            children.classList.add('expanded');
        }
    }
}

// 切换导航项展开/折叠状态
function toggleNavItem(navItem) {
    const children = navItem.querySelector('.nav-children');
    const expandIcon = navItem.querySelector('.expand-icon');

    if (children) {
        const isExpanded = navItem.classList.contains('expanded');

        if (isExpanded) {
            // 折叠
            navItem.classList.remove('expanded');
            children.classList.remove('expanded');
            children.classList.add('collapsed');
        } else {
            // 展开
            navItem.classList.add('expanded');
            children.classList.remove('collapsed');
            children.classList.add('expanded');
        }
    }
}

// 设置初始展开状态
function setInitialExpandState() {
    // 默认展开第一级
    const topLevelSections = document.querySelectorAll('.nav-section[data-level="0"]');
    topLevelSections.forEach(section => {
        const title = section.querySelector('.nav-section-title');
        const children = section.querySelector('.nav-children');

        if (title && children) {
            title.classList.add('expanded');
            children.classList.add('expanded');
        }
    });

    // 展开包含活动项的路径
    const activeItem = document.querySelector('.nav-item.active');
    if (activeItem) {
        expandPathToItem(activeItem);
    }
}

// 展开到指定项的路径
function expandPathToItem(item) {
    let current = item.parentElement;

    while (current && current.classList.contains('nav-children')) {
        current.classList.remove('collapsed');
        current.classList.add('expanded');

        // 找到父级导航项或区域标题
        const parent = current.previousElementSibling;
        if (parent) {
            if (parent.classList.contains('nav-section-title')) {
                parent.classList.add('expanded');
            } else if (parent.classList.contains('nav-link')) {
                parent.parentElement.classList.add('expanded');
            }
        }

        current = current.parentElement.parentElement;
    }
}

// 绑定导航事件
function bindNavigationEvents() {
    const navLinks = document.querySelectorAll('.nav-link[data-doc-id]');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // 如果是有效的文档链接，允许正常跳转
            if (href && href !== '#') {
                // 让浏览器正常处理链接跳转
                return;
            }

            // 对于没有有效链接的项目，阻止默认行为
            e.preventDefault();

            // 移除所有活动状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加当前活动状态
            this.parentElement.classList.add('active');

            // 展开到当前项的路径
            expandPathToItem(this.parentElement);


        });
    });
}

// 目录功能
function initTableOfContents() {
    const tocLinks = document.querySelectorAll('.toc-link');
    
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有活动状态
            document.querySelectorAll('.toc-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加当前活动状态
            this.parentElement.classList.add('active');
            
            // 滚动到对应位置
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
    
    // 自动高亮当前可见的章节
    window.addEventListener('scroll', updateActiveSection);
}

// 版本选择器功能
function initVersionSelector() {
    const versionSelect = document.getElementById('versionSelect');
    
    if (versionSelect) {
        versionSelect.addEventListener('change', function() {
            const selectedVersion = this.value;
            loadVersionContent(selectedVersion);
        });
    }
}



// 更新文档内容
function updateDocumentContent(title) {
    const docTitle = document.getElementById('docTitle');
    const documentContent = document.getElementById('documentContent');

    if (docTitle) {
        docTitle.textContent = title;
    }

    // 这里可以根据标题加载不同的内容
    // 暂时保持现有内容不变
}

// 使用数据更新文档内容
function updateDocumentContentWithData(data) {
    const contentBody = document.querySelector('.content-body');

    if (contentBody && data.bodyHtml) {
        contentBody.innerHTML = data.bodyHtml;
    }

    // 更新文档标题
    const docTitle = document.getElementById('docTitle');
    if (docTitle) {
        docTitle.textContent = data.title;
    }
}

// 更新最后更新时间
function updateLastUpdatedTime(updatedAt) {
    const lastUpdatedElement = document.getElementById('lastUpdated');
    if (lastUpdatedElement && updatedAt) {
        const date = new Date(updatedAt);
        lastUpdatedElement.textContent = date.toLocaleString('zh-CN');
    }
}

// 显示文档错误
function showDocumentError(message) {
    const contentBody = document.querySelector('.content-body');
    if (contentBody) {
        contentBody.innerHTML = `
            <div class="alert alert-danger">
                <h4>加载失败</h4>
                <p>${message}</p>
            </div>
        `;
    }
}

// 生成文档大纲
function generateTableOfContents() {
    console.log('开始生成大纲...');

    const contentContainer = document.querySelector('.content-container');
    const tocContent = document.querySelector('.toc-content');
    const docToc = document.querySelector('.doc-toc');
    const documentContent = document.querySelector('.document-content');

    console.log('contentContainer:', contentContainer);
    console.log('tocContent:', tocContent);

    if (!contentContainer || !tocContent || !docToc) {
        console.log('未找到必要的DOM元素');
        return;
    }

    // 查找所有标题元素
    const headings = contentContainer.querySelectorAll('h1, h2, h3, h4, h5, h6');
    console.log('找到标题数量:', headings.length);
    const nonEmptyHeadings = Array.from(headings).filter(
        heading => heading.innerText  && heading.innerText.trim()  !== ''
    );
    if (nonEmptyHeadings.length === 0) {
        // 隐藏大纲区域
        docToc.style.display = 'none';
        // 调整文档内容宽度
        if (documentContent) {
            documentContent.classList.add('no-toc');
        }
        console.log('没有标题，隐藏大纲');
        return;
    } else {
        // 显示大纲区域
        docToc.style.display = 'block';
        // 恢复文档内容宽度
        if (documentContent) {
            documentContent.classList.remove('no-toc');
        }
    }

    // 为每个标题添加ID（如果没有的话）
    nonEmptyHeadings.forEach((heading, index) => {
        if (!heading.id) {
            heading.id = `heading-${index}`;
        }
        console.log(`标题 ${index + 1}: ${heading.tagName} - ${heading.textContent.trim()}`);
    });

    try {
        // 生成大纲树
        const tocTree = buildTocTree(headings);
        console.log('大纲树:', tocTree);

        // 渲染大纲
        const treeHtml = renderTocTree(tocTree);
        tocContent.innerHTML = `<ul class="toc-tree">${treeHtml}</ul>`;

        // 绑定点击事件
        bindTocEvents();

        // 监听滚动事件，高亮当前章节
        initTocScrollSpy();

        console.log('大纲生成完成');
    } catch (error) {
        console.error('生成大纲时发生错误:', error);
        tocContent.innerHTML = `
            <div class="toc-empty">
                <i class="bi bi-exclamation-triangle"></i>
                <span>大纲生成失败</span>
            </div>
        `;
    }
}

// 构建大纲树结构
function buildTocTree(headings) {
    const tree = [];
    const stack = [];

    headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));
        const item = {
            id: heading.id,
            text: heading.textContent.trim(),
            level: level,
            element: heading,
            children: [],
            hasChildren: false
        };

        // 清理栈，移除级别大于等于当前级别的项目
        while (stack.length > 0 && stack[stack.length - 1].level >= level) {
            stack.pop();
        }

        // 如果栈为空，说明这是顶级项目
        if (stack.length === 0) {
            tree.push(item);
        } else {
            // 否则添加到栈顶项目的子项中
            const parent = stack[stack.length - 1];
            parent.children.push(item);
            parent.hasChildren = true;
        }

        // 将当前项目推入栈
        stack.push(item);
    });

    return tree;
}

// 渲染大纲树
function renderTocTree(tree, parentLevel = 0) {
    return tree.map(item => {
        const hasChildren = item.children.length > 0;
        const isExpanded = parentLevel === 0 || item.level <= 2; // 默认展开前两级
        const childrenHtml = hasChildren ?
            `<ul class="toc-tree toc-children ${isExpanded ? 'expanded' : 'collapsed'}">${renderTocTree(item.children, item.level)}</ul>` : '';

        const expandIcon = hasChildren ?
            `<i class="toc-expand-icon bi bi-chevron-right ${isExpanded ? 'expanded' : ''}"></i>` : '';

        return `
            <li class="toc-item ${hasChildren ? 'has-children' : ''} ${isExpanded ? 'expanded' : ''}" data-level="${item.level}">
                <div class="toc-item-content">
                    ${expandIcon}
                    <a href="#${item.id}" class="toc-link" data-target="${item.id}">
                        ${item.text}
                    </a>
                </div>
                ${childrenHtml}
            </li>
        `;
    }).join('');
}

// 绑定大纲点击事件
function bindTocEvents() {
    const tocLinks = document.querySelectorAll('.toc-link');
    const expandIcons = document.querySelectorAll('.toc-expand-icon');

    // 绑定链接点击事件
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.dataset.target;
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                // 平滑滚动到目标位置
                const headerHeight = document.querySelector('.document-header')?.offsetHeight || 0;
                const targetTop = targetElement.offsetTop - headerHeight - 20;

                window.scrollTo({
                    top: targetTop,
                    behavior: 'smooth'
                });

                // 更新活动状态
                updateTocActiveState(targetId);
            }
        });
    });

    // 绑定展开/折叠事件
    expandIcons.forEach(icon => {
        icon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const tocItem = this.closest('.toc-item');
            const children = tocItem.querySelector('.toc-children');

            if (tocItem && children) {
                const isExpanded = tocItem.classList.contains('expanded');

                if (isExpanded) {
                    // 折叠
                    tocItem.classList.remove('expanded');
                    children.classList.remove('expanded');
                    children.classList.add('collapsed');
                    this.classList.remove('expanded');
                } else {
                    // 展开
                    tocItem.classList.add('expanded');
                    children.classList.remove('collapsed');
                    children.classList.add('expanded');
                    this.classList.add('expanded');
                }
            }
        });
    });
}

// 初始化滚动监听
function initTocScrollSpy() {
    const headings = document.querySelectorAll('.content-container h1, .content-container h2, .content-container h3, .content-container h4, .content-container h5, .content-container h6');

    if (headings.length === 0) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                updateTocActiveState(entry.target.id);
            }
        });
    }, {
        rootMargin: '-20% 0px -70% 0px',
        threshold: 0
    });

    headings.forEach(heading => {
        observer.observe(heading);
    });
}

// 更新大纲活动状态
function updateTocActiveState(activeId) {
    const tocLinks = document.querySelectorAll('.toc-link');

    tocLinks.forEach(link => {
        if (link.dataset.target === activeId) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// 计算阅读时间
function calculateReadingTime() {
    const contentContainer = document.querySelector('.content-container');
    const readingTimeElement = document.querySelector('.reading-time');

    if (!contentContainer) {
        console.log('未找到内容区域');
        return;
    }

    // 获取纯文本内容
    const text = contentContainer.innerText || contentContainer.textContent || '';

    // 计算字符数（包括中英文）
    const totalChars = text.length;

    // 分别计算中文字符和英文单词
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishText = text.replace(/[\u4e00-\u9fa5]/g, ' ');
    const englishWords = englishText.split(/\s+/).filter(word => word.length > 0).length;

    // 阅读速度参考：
    // 中文：每分钟 200-300 字，取中位数 250 字/分钟
    // 英文：每分钟 200-250 词，取中位数 225 词/分钟
    const chineseReadingSpeed = 250; // 字/分钟
    const englishReadingSpeed = 225;  // 词/分钟

    // 计算阅读时间（分钟）
    const chineseTime = chineseChars / chineseReadingSpeed;
    const englishTime = englishWords / englishReadingSpeed;
    const totalMinutes = chineseTime + englishTime;

    // 格式化显示
    let readingTimeText;
    if (totalMinutes < 1) {
        readingTimeText = '少于 1 分钟';
    } else if (totalMinutes < 60) {
        readingTimeText = `约 ${Math.ceil(totalMinutes)} 分钟`;
    } else {
        const hours = Math.floor(totalMinutes / 60);
        const minutes = Math.ceil(totalMinutes % 60);
        if (minutes === 0) {
            readingTimeText = `约 ${hours} 小时`;
        } else {
            readingTimeText = `约 ${hours} 小时 ${minutes} 分钟`;
        }
    }

    console.log(`文本统计: 总字符数 ${totalChars}, 中文字符 ${chineseChars}, 英文单词 ${englishWords}, 预计阅读时间 ${readingTimeText}`);

    // 更新页面显示
    if (readingTimeElement) {
        readingTimeElement.textContent = `阅读时间${readingTimeText}`;
    }

    return {
        totalChars,
        chineseChars,
        englishWords,
        totalMinutes,
        readingTimeText
    };
}

// 初始化首页功能
function initHomepage() {
    // 首页功能已删除
}

// 加载版本内容
function loadVersionContent(version) {
    console.log('切换到版本:', version);
    // 这里可以实现版本切换逻辑
}

// 更新活动章节
function updateActiveSection() {
    const sections = document.querySelectorAll('[id]');
    const tocItems = document.querySelectorAll('.toc-item');
    
    let currentSection = '';
    
    sections.forEach(section => {
        const rect = section.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = section.id;
        }
    });
    
    tocItems.forEach(item => {
        const link = item.querySelector('.toc-link');
        const href = link.getAttribute('href').substring(1);
        
        if (href === currentSection) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}

// 显示加载状态
function showLoadingState() {
    const documentContent = document.getElementById('documentContent');
    if (documentContent) {
        documentContent.style.opacity = '0.5';
    }
}

// 隐藏加载状态
function hideLoadingState() {
    const documentContent = document.getElementById('documentContent');
    if (documentContent) {
        documentContent.style.opacity = '1';
    }
}

// 显示错误状态
function showErrorState() {
    console.error('加载内容时发生错误');
    // 这里可以显示错误提示
}

// 响应式处理
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.doc-sidebar');
    if (window.innerWidth > 768) {
        sidebar.classList.remove('open');
    }
});

// 目录导航功能
function initDirectoriesNavigation() {
    // 绑定区域标题点击事件
    const sectionTitles = document.querySelectorAll('.nav-section-title[data-toggle="section"]');
    sectionTitles.forEach(title => {
        title.addEventListener('click', function(e) {
            e.preventDefault();
            toggleDirectorySection(this);
        });
    });

    // 绑定导航项点击事件
    const navLinks = document.querySelectorAll('.nav-link[data-doc-id]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 如果有真实的slug，允许正常跳转
            const slug = this.dataset.slug;
            if (slug && slug !== '#') {
                return; // 允许正常跳转
            }

            e.preventDefault();
            selectNavigationItem(this);
        });
    });

    // 初始化展开状态
    initDirectoriesExpandState();
}

// 切换目录区域展开/折叠
function toggleDirectorySection(sectionTitle) {
    const section = sectionTitle.closest('.nav-section');
    const children = section.querySelector('.nav-children');
    const expandIcon = sectionTitle.querySelector('.expand-icon');

    // 清除所有选中状态
    document.querySelectorAll('.nav-item.active').forEach(item => {
        item.classList.remove('active');
    });

    document.querySelectorAll('.nav-section-title.active').forEach(title => {
        title.classList.remove('active');
    });

    // 选中当前区域标题
    sectionTitle.classList.add('active');

    if (children && expandIcon) {
        const isExpanded = sectionTitle.classList.contains('expanded');

        if (isExpanded) {
            // 折叠
            sectionTitle.classList.remove('expanded');
            children.classList.remove('expanded');
            children.classList.add('collapsed');
        } else {
            // 展开
            sectionTitle.classList.add('expanded');
            children.classList.remove('collapsed');
            children.classList.add('expanded');
        }
    }

    // 更新内容
    const sectionName = sectionTitle.querySelector('.section-name').textContent;
    updateDocumentContent(sectionName);
}

// 选择导航项
function selectNavigationItem(navLink) {
    const href = navLink.getAttribute('href');

    // 如果是有效的文档链接，允许正常跳转
    if (href && href !== '#') {
        // 让浏览器正常处理链接跳转
        window.location.href = href;
        return;
    }

    // 移除所有活动状态
    document.querySelectorAll('.nav-item.active').forEach(item => {
        item.classList.remove('active');
    });

    // 移除区域标题的活动状态
    document.querySelectorAll('.nav-section-title.active').forEach(title => {
        title.classList.remove('active');
    });

    // 只添加当前点击项的活动状态
    const navItem = navLink.closest('.nav-item');
    if (navItem) {
        navItem.classList.add('active');
    }

    // 展开到当前项的路径
    expandPathToNavItem(navItem);


}

// 展开到指定导航项的路径
function expandPathToNavItem(navItem) {
    let current = navItem.parentElement;

    while (current && current.classList.contains('nav-children')) {
        current.classList.remove('collapsed');
        current.classList.add('expanded');

        // 找到父级区域标题
        const parentSection = current.closest('.nav-section');
        if (parentSection) {
            const sectionTitle = parentSection.querySelector('.nav-section-title');
            if (sectionTitle) {
                sectionTitle.classList.add('expanded');
            }
        }

        current = current.parentElement.parentElement;
    }
}

// 初始化目录展开状态
function initDirectoriesExpandState() {
    // 展开所有级别的菜单
    const allSections = document.querySelectorAll('.nav-section');
    allSections.forEach(section => {
        const title = section.querySelector('.nav-section-title');
        const children = section.querySelector('.nav-children');

        if (title) {
            title.classList.add('expanded');
        }
        if (children) {
            children.classList.add('expanded');
            children.classList.remove('collapsed');
        }
    });
}

// 添加动画效果
function addNavigationAnimations() {
    // 为导航项添加进入动画
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 50}ms`;
        item.classList.add('nav-item-animate');
    });
}

// 搜索导航项
function searchNavigationItems(query) {
    const navItems = document.querySelectorAll('.nav-item');
    const lowerQuery = query.toLowerCase();

    navItems.forEach(item => {
        const navName = item.querySelector('.nav-name');
        const navDesc = item.querySelector('.nav-desc');

        if (navName) {
            const nameText = navName.textContent.toLowerCase();
            const descText = navDesc ? navDesc.textContent.toLowerCase() : '';

            if (nameText.includes(lowerQuery) || descText.includes(lowerQuery)) {
                item.style.display = 'block';
                // 展开包含匹配项的区域
                expandPathToNavItem(item);
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
