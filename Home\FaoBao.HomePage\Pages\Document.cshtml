﻿@page "/docs/{slug?}"

@model FaoBao.HomePage.Pages.DocumentModel
@{
    ViewData["Title"] = "文档中心 - 法宝";
    ViewData["Description"] = "法宝文档中心，提供完整的开发指南和API文档";
}

@section Styles {
    <link href="/css/document.css" rel="stylesheet" asp-append-version="true"/>
}

@* @section Scripts { *@
@*     <script src="/js/document.js"></script> *@
@* } *@

<!-- Hero Section -->
<section id="help-hero" class="help-hero section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center" data-aos="fade-up">
                <h1>文档中心</h1>
                <p>快速找到您需要的答案，让使用更简单</p>
                <div class="search-box">
                    <form method="get" asp-page="/DocumentSearch">
                        <input
                            type="text"
                            name="keyword"
                          
                            placeholder="搜索文档、功能或问题..."
                            class="form-control"/>
                        <button type="submit" class="search-btn">
                            <i class="bi bi-search"></i>
                        </button>
                    </form>

                </div>
            </div>
        </div>
    </div>
</section>

<!-- Help Categories Section -->
<section id="help-categories" class="help-categories section">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3">
                <div class="help-sidebar" data-aos="fade-right">
                    <h4>文档分类</h4>
                    <ul class="category-list">
                      @if (Model.Categories != null)
                      {
                          @foreach (var category in Model.Categories)
                          {
                              <li
                                  class="category-item @(category.IsActive ? "active" : "")"
                                  data-category="@category.Slug">
                                  <div class="category-icon">
                                      <img
                                          src="//cdn.fabao.com/icons/130/@(category.Slug).png"
                                          alt="@category.Name"/>
                                  </div>
                                  <div class="category-content">
                                      <a asp-page="/Document" asp-route-slug="@category.Slug">
                                          <span class="category-title">@category.Title</span>
                                      </a>
                                  </div>
                                  <div class="category-count">
                                      <span class="count-badge">@category.ItemsCount</span>
                                  </div>

                              </li>
                          }
                      }

                    </ul>
                </div>
            </div>

            <!-- Content Area -->
            <div class="col-lg-9">
                <!-- Document List View -->
                <div id="documentList" class="document-list" data-aos="fade-left">
                    <div class="category-header">
                        <h3 id="categoryTitle">@Model.CurrentCategory.Name</h3>
                        <p id="categoryDescription">@Model.CurrentCategory.Description</p>
                    </div>

                    <div class="documents-grid">
                        @if (Model.CurrentDocuments != null)
                        {
                            @foreach (var document in Model.CurrentDocuments)
                            {
                                <div class="document-card">
                                    <div class="document-content">
                                        <a asp-page="/DocumentDetail" asp-route-book="@Model.CurrentCategory.Slug"
                                           asp-route-slug="@document.Slug">
                                            <h4>@document.Title</h4>
                                            <p>@document.Description</p>
                                            <div class="doc-meta">
                                                <span class="update-time">更新于 @document.UpdateTime</span>

                                            </div>
                                        </a>
                                    </div>
                                    <div class="document-actions">
                                        <i class="bi bi-chevron-right doc-arrow"></i>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="col-12 text-center">
                                <div class="no-results">
                                    <i class="bi bi-search"
                                       style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
                                    <h4>该分类下暂无文档</h4>
                                    <p>请尝试浏览其他分类的文档</p>
                                </div>
                            </div>
                        }

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
