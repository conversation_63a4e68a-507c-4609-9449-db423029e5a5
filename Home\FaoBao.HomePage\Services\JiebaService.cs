﻿using JiebaNet.Segmenter;
using System.Text;

namespace FaoBao.HomePage.Services;

/// <summary>
/// Jieba中文分词服务 - 针对电商行业优化
/// </summary>
public class JiebaService
{
    private readonly JiebaSegmenter _segmenter;
    private readonly HashSet<string> _stopWords;
    private readonly ILogger<JiebaService> _logger;
    private readonly IWebHostEnvironment _environment;

    public JiebaService(ILogger<JiebaService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _environment = environment;
        _stopWords = new HashSet<string>();

        try
        {
            _segmenter = new JiebaSegmenter();
            InitializeJieba();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化Jieba分词器失败，将使用简单分词模式");
            _segmenter = null!; // 将在分词方法中处理
        }
    }

    /// <summary>
    /// 初始化Jieba分词器
    /// </summary>
    private void InitializeJieba()
    {
        if (_segmenter == null)
            return;

        try
        {
            var resourcesPath = Path.Combine(_environment.ContentRootPath, "Resources");

            // 加载电商专用词典
            var ecommerceDict = Path.Combine(resourcesPath, "ecommerce_dict.txt");
            if (File.Exists(ecommerceDict))
            {
                _segmenter.LoadUserDict(ecommerceDict);
                _logger.LogInformation("成功加载电商专用词典: {DictPath}", ecommerceDict);
            }
            else
            {
                _logger.LogWarning("电商专用词典文件不存在: {DictPath}", ecommerceDict);
            }

            // 加载停用词
            LoadStopWords(resourcesPath);

            _logger.LogInformation("Jieba分词服务初始化完成，电商词典已加载");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化Jieba分词服务时发生错误");
            // 不再抛出异常，允许服务继续运行
        }
    }

    /// <summary>
    /// 加载停用词
    /// </summary>
    /// <param name="resourcesPath">资源文件路径</param>
    private void LoadStopWords(string resourcesPath)
    {
        try
        {
            var stopWordsFile = Path.Combine(resourcesPath, "stopwords.txt");
            if (File.Exists(stopWordsFile))
            {
                var lines = File.ReadAllLines(stopWordsFile, Encoding.UTF8);
                foreach (var line in lines)
                {
                    var word = line.Trim();
                    if (!string.IsNullOrEmpty(word) && !word.StartsWith("#"))
                    {
                        _stopWords.Add(word);
                    }
                }
                _logger.LogInformation("成功加载停用词 {Count} 个", _stopWords.Count);
            }
            else
            {
                _logger.LogWarning("停用词文件不存在: {StopWordsFile}", stopWordsFile);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载停用词时发生错误");
        }
    }

    /// <summary>
    /// 对文本进行分词
    /// </summary>
    /// <param name="text">要分词的文本</param>
    /// <param name="filterStopWords">是否过滤停用词</param>
    /// <returns>分词结果</returns>
    public List<string> SegmentText(string text, bool filterStopWords = true)
    {
        if (string.IsNullOrWhiteSpace(text))
            return new List<string>();

        try
        { // 使用jieba分词器
            List<string> segments = _segmenter.CutForSearch(text, hmm: false).ToList();

            var result = segments
                .Where(word => !string.IsNullOrWhiteSpace(word) && word.Length > 1)
                .ToList();

            // 过滤停用词
            if (filterStopWords)
            {
                result = result.Where(word => !_stopWords.Contains(word)).ToList();
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分词时发生错误: {Text}", text.Substring(0, Math.Min(text.Length, 100)));
            return SimpleFallbackSegment(text);
        }
    }

    /// <summary>
    /// 对文本进行分词并返回用空格连接的字符串
    /// </summary>
    /// <param name="text">要分词的文本</param>
    /// <param name="filterStopWords">是否过滤停用词</param>
    /// <returns>用空格连接的分词结果</returns>
    public string SegmentTextToString(string text, bool filterStopWords = true)
    {
        var segments = SegmentText(text, filterStopWords);
        return string.Join(" ", segments);
    }

    /// <summary>
    /// 对搜索查询进行分词优化
    /// </summary>
    /// <param name="query">搜索查询</param>
    /// <returns>优化后的查询词</returns>
    public string OptimizeSearchQuery(string query)
    {
        if (string.IsNullOrWhiteSpace(query))
            return string.Empty;

        try
        {
            // 对查询进行分词，保留重要词汇
            var segments = SegmentText(query, filterStopWords: true);
            
            // 如果分词后没有有效词汇，返回原查询
            if (!segments.Any())
                return query.Trim();

            // 返回分词结果，用空格连接
            return string.Join(" ", segments);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "优化搜索查询时发生错误: {Query}", query);
            return query.Trim();
        }
    }

    /// <summary>
    /// 为电商内容生成搜索关键词
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="content">内容</param>
    /// <returns>搜索关键词</returns>
    public string GenerateSearchKeywords(string title, string content)
    {
        try
        {
            var allKeywords = new List<string>();

            // 处理标题（权重更高，不过滤停用词）
            if (!string.IsNullOrWhiteSpace(title))
            {
                var titleKeywords = SegmentText(title, filterStopWords: false);
                allKeywords.AddRange(titleKeywords);
            }

            // 处理内容（过滤停用词）
            if (!string.IsNullOrWhiteSpace(content))
            {
                var contentKeywords = SegmentText(content, filterStopWords: true);
                allKeywords.AddRange(contentKeywords);
            }

            // 去重并返回
            var uniqueKeywords = allKeywords.Distinct().ToList();
            return string.Join(" ", uniqueKeywords);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成搜索关键词时发生错误");
            return string.Empty;
        }
    }

    /// <summary>
    /// 简单的回退分词方法（当jieba初始化失败时使用）
    /// </summary>
    /// <param name="text">要分词的文本</param>
    /// <returns>分词结果</returns>
    private List<string> SimpleFallbackSegment(string text)
    {
        // 简单的基于标点符号和空格的分词
        var separators = new char[] { ' ', '，', '。', '！', '？', '；', '：', '"', '"', '（', '）', '【', '】', '\n', '\r', '\t' };
        return text.Split(separators, StringSplitOptions.RemoveEmptyEntries)
                   .Where(word => !string.IsNullOrWhiteSpace(word) && word.Length > 1)
                   .ToList();
    }
}
