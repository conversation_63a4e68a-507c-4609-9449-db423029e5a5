﻿@page "/news"
@model FaoBao.HomePage.Pages.ArticlesModel
@{
    ViewData["Title"] = "技术文章 - 法宝";
    ViewData["Description"] = "法宝技术文章中心，分享数字化产业洞察、产品动态、技术实践和商业案例";
    ViewData["Keywords"] = "法宝,技术文章,数字化,电商自动化,产品动态,技术分享";
}

@section Styles {
    <link href="/css/articles.css" rel="stylesheet" asp-append-version="true"/>
}

@section Scripts {
    <script src="/js/articles.js" asp-append-version="true"></script>
}

<!-- Hero Section -->
<section class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8" data-aos="fade-up">
                <h1 class="display-4 fw-bold mb-4">技术文章</h1>
                <p class="lead mb-4">探索数字化产业前沿，分享技术实践与商业洞察</p>
                
                <!-- Search Bar -->
                <div class="search-container mx-auto" style="max-width: 600px;">
                    <form method="get" class="d-flex">
                        <div class="input-group input-group-lg">
                            <input type="text" 
                                   name="search" 
                                   value="@Model.Search"
                                   class="form-control" 
                                   placeholder="搜索文章标题、内容..."
                                   aria-label="搜索文章">
                            <button class="btn btn-light" type="submit">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="sidebar-sticky">
                    <!-- Categories -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white border-0 py-3">
                            <h5 class="mb-0 fw-bold">
                                <i class="bi bi-folder me-2"></i>文章分类
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <a asp-page="/Articles" 
                                   class="list-group-item list-group-item-action border-0 @(string.IsNullOrEmpty(Model.Category) ? "active" : "")">
                                    <i class="bi bi-grid me-2"></i>全部文章
                                    <span class="badge bg-primary rounded-pill float-end">@Model.PageData.TotalCount</span>
                                </a>
                                @foreach (var category in Model.PageData.Categories)
                                {
                                    <a asp-page="/Articles" 
                                       asp-route-category="@category.Id"
                                       class="list-group-item list-group-item-action border-0 @(Model.Category == category.Id ? "active" : "")">
                                        <i class="@category.Icon me-2"></i>@category.Name
                                    </a>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Featured Articles -->
                    @if (Model.PageData.FeaturedArticles.Any())
                    {
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white border-0 py-3">
                                <h5 class="mb-0 fw-bold">
                                    <i class="bi bi-star me-2"></i>推荐阅读
                                </h5>
                            </div>
                            <div class="card-body">
                                @foreach (var article in Model.PageData.FeaturedArticles.Take(5))
                                {
                                    <div class="featured-article-item mb-3 pb-3 @(article != Model.PageData.FeaturedArticles.Take(5).Last() ? "border-bottom" : "")">
                                        <h6 class="mb-2">
                                            <a asp-page="/ArticleDetail" 
                                               asp-route-slug="@article.Slug" 
                                               class="text-decoration-none">
                                                @article.Title
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar me-1"></i>@article.PublishDate.ToString("MM-dd")
                                            <i class="bi bi-eye ms-2 me-1"></i>@article.ViewCount
                                        </small>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Articles List -->
            <div class="col-lg-9">
                <!-- Filter Info -->
                @if (!string.IsNullOrEmpty(Model.Search) || !string.IsNullOrEmpty(Model.Category))
                {
                    <div class="alert alert-info border-0 mb-4" data-aos="fade-up">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="bi bi-funnel me-2"></i>
                                @if (!string.IsNullOrEmpty(Model.Search))
                                {
                                    <span>搜索 "<strong>@Model.Search</strong>" </span>
                                }
                                @if (!string.IsNullOrEmpty(Model.Category))
                                {
                                    var categoryName = Model.PageData.Categories.FirstOrDefault(c => c.Id == Model.Category)?.Name ?? Model.Category;
                                    <span>分类 "<strong>@categoryName</strong>" </span>
                                }
                                <span>共找到 <strong>@Model.PageData.TotalCount</strong> 篇文章</span>
                            </div>
                            <a asp-page="/Articles" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>清除筛选
                            </a>
                        </div>
                    </div>
                }

                <!-- Articles Grid -->
                @if (Model.PageData.Articles.Any())
                {
                    <div class="row g-4">
                        @for (int i = 0; i < Model.PageData.Articles.Count; i++)
                        {
                            var article = Model.PageData.Articles[i];
                            <div class="col-md-6 col-xl-4" data-aos="fade-up" data-aos-delay="@(i * 100)">
                                <article class="card h-100 border-0 shadow-sm article-card">
                                    @if (!string.IsNullOrEmpty(article.FeaturedImage))
                                    {
                                        <div class="article-image">
                                            <img src="@article.FeaturedImage" 
                                                 class="card-img-top" 
                                                 alt="@article.Title"
                                                 loading="lazy">
                                            @if (article.IsFeatured)
                                            {
                                                <span class="badge bg-warning text-dark featured-badge">
                                                    <i class="bi bi-star-fill me-1"></i>推荐
                                                </span>
                                            }
                                        </div>
                                    }
                                    
                                    <div class="card-body d-flex flex-column">
                                        <!-- Category Badge -->
                                        <div class="mb-2">
                                            <span class="badge <EMAIL> bg-opacity-10 text-@article.<NAME_EMAIL> border-opacity-25">
                                                @article.CategoryName
                                            </span>
                                        </div>

                                        <!-- Title -->
                                        <h5 class="card-title mb-3">
                                            <a asp-page="/ArticleDetail" 
                                               asp-route-slug="@article.Slug" 
                                               class="text-decoration-none text-dark">
                                                @article.Title
                                            </a>
                                        </h5>

                                        <!-- Summary -->
                                        <p class="card-text text-muted mb-3 flex-grow-1">
                                            @article.Summary
                                        </p>

                                        <!-- Tags -->
                                        @if (article.Tags.Any())
                                        {
                                            <div class="article-tags mb-3">
                                                @foreach (var tag in article.Tags.Take(3))
                                                {
                                                    <span class="badge bg-light text-muted me-1 mb-1">
                                                        #@tag.Name
                                                    </span>
                                                }
                                            </div>
                                        }

                                        <!-- Meta Info -->
                                        <div class="article-meta d-flex justify-content-between align-items-center text-muted small">
                                            <div class="author-info d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(article.AuthorAvatar))
                                                {
                                                    <img src="@article.AuthorAvatar" 
                                                         class="rounded-circle me-2" 
                                                         width="24" height="24" 
                                                         alt="@article.Author">
                                                }
                                                <span>@article.Author</span>
                                            </div>
                                            <div class="article-stats">
                                                <span class="me-2">
                                                    <i class="bi bi-clock me-1"></i>@article.ReadingTime 分钟
                                                </span>
                                                <span>
                                                    <i class="bi bi-eye me-1"></i>@article.ViewCount
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        }
                    </div>

                    <!-- Pagination -->
                    @if (Model.PageData.TotalPages > 1)
                    {
                        <nav aria-label="文章分页" class="mt-5">
                            <ul class="pagination justify-content-center">
                                @if (Model.CurrentPage > 1)
                                {
                                    <li class="page-item">
                                        <a class="page-link" 
                                           asp-page="/Articles" 
                                           asp-route-page="@(Model.CurrentPage - 1)"
                                           asp-route-category="@Model.Category"
                                           asp-route-search="@Model.Search">
                                            <i class="bi bi-chevron-left"></i>
                                        </a>
                                    </li>
                                }

                                @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.PageData.TotalPages, Model.CurrentPage + 2); i++)
                                {
                                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                        <a class="page-link" 
                                           asp-page="/Articles" 
                                           asp-route-page="@i"
                                           asp-route-category="@Model.Category"
                                           asp-route-search="@Model.Search">
                                            @i
                                        </a>
                                    </li>
                                }

                                @if (Model.CurrentPage < Model.PageData.TotalPages)
                                {
                                    <li class="page-item">
                                        <a class="page-link" 
                                           asp-page="/Articles" 
                                           asp-route-page="@(Model.CurrentPage + 1)"
                                           asp-route-category="@Model.Category"
                                           asp-route-search="@Model.Search">
                                            <i class="bi bi-chevron-right"></i>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }
                }
                else
                {
                    <!-- No Articles Found -->
                    <div class="text-center py-5" data-aos="fade-up">
                        <div class="mb-4">
                            <i class="bi bi-journal-x display-1 text-muted"></i>
                        </div>
                        <h3 class="text-muted mb-3">暂无文章</h3>
                        <p class="text-muted mb-4">
                            @if (!string.IsNullOrEmpty(Model.Search) || !string.IsNullOrEmpty(Model.Category))
                            {
                                <span>没有找到符合条件的文章，请尝试其他搜索条件。</span>
                            }
                            else
                            {
                                <span>还没有发布任何文章，请稍后再来查看。</span>
                            }
                        </p>
                        <a asp-page="/Articles" class="btn btn-primary">
                            <i class="bi bi-arrow-left me-2"></i>返回全部文章
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</section>
