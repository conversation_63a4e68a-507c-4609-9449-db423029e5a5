﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace FaoBao.HomePage.Common;

[HtmlTargetElement("menu", Attributes = "asp-page")]
public class ActiveMenuTagHelper : TagHelper
{    private readonly IUrlHelperFactory _urlHelperFactory;
 
    public ActiveMenuTagHelper(IUrlHelperFactory urlHelperFactory)
    {
        _urlHelperFactory = urlHelperFactory;
    }
    [ViewContext]
    [HtmlAttributeNotBound]
    public required ViewContext ViewContext { get; set; }

    [HtmlAttributeName("asp-page")]
    public required string Page { get; set; }

    public override void Process(TagHelperContext context, TagHelperOutput output)
    {
        // 优先使用 ViewData["ActiveMenu"]
        var activeMenu = ViewContext.ViewData["ActiveMenu"]?.ToString();
        bool isActive;
        if (!string.IsNullOrEmpty(activeMenu))
        {
            isActive = string.Equals(activeMenu, Page, StringComparison.OrdinalIgnoreCase);
        }
        else
        {
            // 自动判断当前页面
            var currentPage = ViewContext.RouteData.Values["page"]?.ToString();
            isActive = string.Equals(currentPage, Page, StringComparison.OrdinalIgnoreCase);
        }
        if (isActive)
        {
            var existingClass = output.Attributes["class"]?.Value?.ToString() ?? "";
            output.Attributes.SetAttribute("class", $"{existingClass} active".Trim());
        }
        output.TagName = "a";
        // 设置 href 属性
     
        var urlHelper = _urlHelperFactory.GetUrlHelper(ViewContext);
        var pageUrl = urlHelper.Page(Page); // Page 为 asp-page 的值
        output.Attributes.SetAttribute("href", pageUrl);
    }
}