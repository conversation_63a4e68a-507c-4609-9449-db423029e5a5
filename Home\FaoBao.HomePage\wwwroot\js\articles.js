﻿/**
 * Articles Page JavaScript
 * 处理文章列表页面的交互功能
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeArticlesPage();
});

function initializeArticlesPage() {
    // 初始化搜索功能
    initializeSearch();
    
    // 初始化文章卡片动画
    initializeCardAnimations();
    
    // 初始化懒加载
    initializeLazyLoading();
}

/**
 * 初始化搜索功能
 */
function initializeSearch() {
    const searchForm = document.querySelector('.search-container form');
    const searchInput = document.querySelector('.search-container input[name="search"]');
    
    if (searchForm && searchInput) {
        // 搜索输入框回车事件
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchForm.submit();
            }
        });
        
        // 搜索按钮点击事件
        const searchButton = searchForm.querySelector('button[type="submit"]');
        if (searchButton) {
            searchButton.addEventListener('click', function(e) {
                e.preventDefault();
                searchForm.submit();
            });
        }
        
        // 清空搜索
        const clearButton = document.querySelector('.btn:contains("清除筛选")');
        if (clearButton) {
            clearButton.addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = '/Articles';
            });
        }
    }
}

/**
 * 初始化文章卡片动画
 */
function initializeCardAnimations() {
    const articleCards = document.querySelectorAll('.article-card');
    
    articleCards.forEach(card => {
        // 鼠标悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        // 点击效果
        const titleLink = card.querySelector('.card-title a');
        if (titleLink) {
            card.addEventListener('click', function(e) {
                // 如果点击的不是链接本身，则触发链接点击
                if (e.target !== titleLink && !titleLink.contains(e.target)) {
                    titleLink.click();
                }
            });
            
            // 添加指针样式
            card.style.cursor = 'pointer';
        }
    });
}

/**
 * 初始化懒加载
 */
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });
        
        const lazyImages = document.querySelectorAll('img[loading="lazy"]');
        lazyImages.forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * 分页导航增强
 */
function enhancePagination() {
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 添加加载状态
            const spinner = document.createElement('div');
            spinner.className = 'spinner-border spinner-border-sm me-2';
            spinner.setAttribute('role', 'status');
            
            this.insertBefore(spinner, this.firstChild);
            this.classList.add('disabled');
        });
    });
}

/**
 * 分类筛选增强
 */
function enhanceCategoryFilter() {
    const categoryLinks = document.querySelectorAll('.list-group-item');
    
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 移除其他活动状态
            categoryLinks.forEach(l => l.classList.remove('active'));
            // 添加当前活动状态
            this.classList.add('active');
        });
    });
}

/**
 * 响应式处理
 */
function handleResponsive() {
    const sidebar = document.querySelector('.sidebar-sticky');
    const mainContent = document.querySelector('.col-lg-9');
    
    function checkScreenSize() {
        if (window.innerWidth < 992) {
            // 移动端：侧边栏移到底部
            if (sidebar && mainContent) {
                sidebar.style.position = 'static';
                sidebar.style.top = 'auto';
            }
        } else {
            // 桌面端：恢复粘性定位
            if (sidebar) {
                sidebar.style.position = 'sticky';
                sidebar.style.top = '100px';
            }
        }
    }
    
    // 初始检查
    checkScreenSize();
    
    // 窗口大小改变时检查
    window.addEventListener('resize', checkScreenSize);
}

/**
 * 工具函数：防抖
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 工具函数：节流
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 初始化响应式处理
document.addEventListener('DOMContentLoaded', function() {
    handleResponsive();
    enhancePagination();
    enhanceCategoryFilter();
});

// 页面滚动时的处理
window.addEventListener('scroll', throttle(function() {
    // 可以在这里添加滚动相关的功能
    // 比如：回到顶部按钮的显示/隐藏
}, 100));
