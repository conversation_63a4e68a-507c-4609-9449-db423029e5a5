using Aliyun.OSS;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using Aliyun.OSS.Common;

namespace FaoBao.HomePage.Services;

/// <summary>
/// 阿里云 OSS 服务
/// </summary>
public class OssService
{
    private readonly OssClient _ossClient;
    private readonly string _bucketName;
    private readonly string _cachePrefix;
    private readonly ILogger<OssService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public OssService(IConfiguration configuration, ILogger<OssService> logger)
    {
        _logger = logger;

        var ossConfig = configuration.GetSection("AliyunOSS");
        var accessKeyId = ossConfig["AccessKeyId"];
        var accessKeySecret = ossConfig["AccessKeySecret"];
        var endpoint = ossConfig["Endpoint"];
        _bucketName = ossConfig["BucketName"] ?? throw new ArgumentNullException("BucketName");
        _cachePrefix = ossConfig["CachePrefix"] ?? string.Empty;

        if (string.IsNullOrEmpty(accessKeyId) || string.IsNullOrEmpty(accessKeySecret) || string.IsNullOrEmpty(endpoint))
        {
            throw new ArgumentException("OSS configuration is incomplete");
        }

        _ossClient = new OssClient(endpoint, accessKeyId, accessKeySecret);
        _jsonOptions = new JsonSerializerOptions
        {
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        };
    }

    /// <summary>
    /// 检查缓存文件是否存在
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(string cacheKey)
    {
        try
        {
            var objectKey = GetObjectKey(cacheKey);
            var result = await Task.Run(() => _ossClient.DoesObjectExist(_bucketName, objectKey));
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查 OSS 对象是否存在时发生错误: {CacheKey}", cacheKey);
            return false;
        }
    }

    /// <summary>
    /// 从 OSS 获取缓存数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>缓存的数据，如果不存在则返回 null</returns>
    public async Task<T?> GetAsync<T>(string cacheKey) where T : class
    {
        try
        {
            var objectKey = GetObjectKey(cacheKey);

            var result = await Task.Run(() => _ossClient.GetObject(_bucketName, objectKey));
            await using var stream = result.Content;
            using var reader = new StreamReader(stream, Encoding.UTF8);
            var content = await reader.ReadToEndAsync();

            if (string.IsNullOrEmpty(content))
                return null;

            return JsonSerializer.Deserialize<T>(content, _jsonOptions);
        }
        catch (OssException ex) when (ex.ErrorCode == "NoSuchKey")
        {
            _logger.LogDebug("OSS 缓存不存在: {CacheKey}", cacheKey);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从 OSS 获取缓存时发生错误: {CacheKey}", cacheKey);
            return null;
        }
    }

    /// <summary>
    /// 将数据存储到 OSS 缓存
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="data">要缓存的数据</param>
    /// <returns>是否成功</returns>
    public async Task<bool> SetAsync<T>(string cacheKey, T data) where T : class
    {
        try
        {
            var objectKey = GetObjectKey(cacheKey);
            var json = JsonSerializer.Serialize(data, _jsonOptions);
            var content = Encoding.UTF8.GetBytes(json);

            using var stream = new MemoryStream(content);

            var metadata = new ObjectMetadata
            {
                ContentType = "application/json",
                ContentLength = content.Length
            };
            await Task.Run(() => _ossClient.PutObject(_bucketName, objectKey, stream, metadata));

            _logger.LogDebug("成功将数据缓存到 OSS: {CacheKey}", cacheKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "将数据存储到 OSS 缓存时发生错误: {CacheKey}", cacheKey);
            return false;
        }
    }

    /// <summary>
    /// 删除 OSS 缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteAsync(string cacheKey)
    {
        try
        {
            var objectKey = GetObjectKey(cacheKey);
            await Task.Run(() => _ossClient.DeleteObject(_bucketName, objectKey));

            _logger.LogDebug("成功删除 OSS 缓存: {CacheKey}", cacheKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除 OSS 缓存时发生错误: {CacheKey}", cacheKey);
            return false;
        }
    }

    /// <summary>
    /// 生成缓存键对应的 OSS 对象键
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>OSS 对象键</returns>
    private string GetObjectKey(string cacheKey)
    {
        // 保留目录结构，只替换不安全的字符
        var safeKey = cacheKey.Replace("\\", "/").Replace(":", "_");
        return $"{_cachePrefix}{safeKey}.json";
    }

    /// <summary>
    /// 生成基于参数的缓存键
    /// </summary>
    /// <param name="method">方法名</param>
    /// <param name="parameters">参数</param>
    /// <returns>缓存键</returns>
    public static string GenerateCacheKey(string method, params string[] parameters)
    {
        var key = $"{method}";
        if (parameters.Length > 0)
        {
            key += "_" + string.Join("_", parameters);
        }
        return key;
    }

    public void Dispose()
    {
        // OSS 客户端不需要显式释放资源
    }
}
