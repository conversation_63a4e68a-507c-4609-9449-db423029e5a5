﻿namespace FaoBao.HomePage.Models
{
    /// <summary>
    /// 文章分类
    /// </summary>
    public class ArticleCategory
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public int Order { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 文章标签
    /// </summary>
    public class ArticleTag
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 文章
    /// </summary>
    public class Article
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string FeaturedImage { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string AuthorAvatar { get; set; } = string.Empty;
        public string CategoryId { get; set; } = string.Empty;
        public List<string> TagIds { get; set; } = new();
        public DateTime PublishDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int ViewCount { get; set; }
        public int ReadingTime { get; set; } // 阅读时间（分钟）
        public bool IsPublished { get; set; } = true;
        public bool IsFeatured { get; set; } = false;
        public int Order { get; set; }
        
        // SEO相关
        public string MetaDescription { get; set; } = string.Empty;
        public string MetaKeywords { get; set; } = string.Empty;
    }

    /// <summary>
    /// 文章列表项（用于列表页显示）
    /// </summary>
    public class ArticleListItem
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public string FeaturedImage { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string AuthorAvatar { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string CategoryColor { get; set; } = string.Empty;
        public List<ArticleTag> Tags { get; set; } = new();
        public DateTime PublishDate { get; set; }
        public int ViewCount { get; set; }
        public int ReadingTime { get; set; }
        public bool IsFeatured { get; set; } = false;
    }

    /// <summary>
    /// 文章详情（用于详情页显示）
    /// </summary>
    public class ArticleDetail
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string FeaturedImage { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string AuthorAvatar { get; set; } = string.Empty;
        public ArticleCategory Category { get; set; } = new();
        public List<ArticleTag> Tags { get; set; } = new();
        public DateTime PublishDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int ViewCount { get; set; }
        public int ReadingTime { get; set; }
        public string MetaDescription { get; set; } = string.Empty;
        public string MetaKeywords { get; set; } = string.Empty;
        
        // 相关文章
        public List<ArticleListItem> RelatedArticles { get; set; } = new();
    }

    /// <summary>
    /// 文章页面数据
    /// </summary>
    public class ArticlePageData
    {
        public List<ArticleCategory> Categories { get; set; } = new();
        public List<ArticleTag> Tags { get; set; } = new();
        public List<ArticleListItem> Articles { get; set; } = new();
        public List<ArticleListItem> FeaturedArticles { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 12;
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// 文章搜索结果
    /// </summary>
    public class ArticleSearchResult
    {
        public List<ArticleListItem> Articles { get; set; } = new();
        public string Keyword { get; set; } = string.Empty;
        public string CategoryId { get; set; } = string.Empty;
        public List<string> TagIds { get; set; } = new();
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// 文章数据（用于JSON反序列化）
    /// </summary>
    public class ArticleData
    {
        public List<ArticleCategory> Categories { get; set; } = new();
        public List<ArticleTag> Tags { get; set; } = new();
        public List<Article> Articles { get; set; } = new();
    }
}
