﻿@page
@model FaoBao.HomePage.Pages.JiebaTestModel
@{
    ViewData["Title"] = "Jieba分词测试";
}

<div class="container mt-4">
    <h2>Jieba中文分词测试 - 电商优化版</h2>
    
    <form method="post" class="mt-4">
        <div class="mb-3">
            <label for="testText" class="form-label">输入测试文本：</label>
            <textarea class="form-control" id="testText" name="TestText" rows="3" placeholder="例如：我想在淘宝上买一个很好的手机">@Model.TestText</textarea>
        </div>
        <button type="submit" class="btn btn-primary">开始分词测试</button>
    </form>

    @if (!string.IsNullOrEmpty(Model.TestText))
    {
        <div class="mt-4">
            <h4>分词结果：</h4>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5>原始文本</h5>
                </div>
                <div class="card-body">
                    <p>@Model.TestText</p>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5>分词结果（包含停用词）</h5>
                </div>
                <div class="card-body">
                    <p>
                        @foreach (var word in Model.SegmentsWithStopWords)
                        {
                            <span class="badge bg-secondary me-1">@word</span>
                        }
                    </p>
                    <small class="text-muted">共 @Model.SegmentsWithStopWords.Count 个词</small>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5>分词结果（过滤停用词）</h5>
                </div>
                <div class="card-body">
                    <p>
                        @foreach (var word in Model.SegmentsWithoutStopWords)
                        {
                            <span class="badge bg-primary me-1">@word</span>
                        }
                    </p>
                    <small class="text-muted">共 @Model.SegmentsWithoutStopWords.Count 个词</small>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5>搜索关键词（用于MeiliSearch）</h5>
                </div>
                <div class="card-body">
                    <p><code>@Model.SearchKeywords</code></p>
                    <small class="text-muted">这些关键词将被用于MeiliSearch索引</small>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5>优化后的搜索查询</h5>
                </div>
                <div class="card-body">
                    <p><code>@Model.OptimizedQuery</code></p>
                    <small class="text-muted">这是用于搜索的优化查询</small>
                </div>
            </div>
        </div>
    }

    <div class="mt-5">
        <h4>测试示例：</h4>
        <div class="row">
            <div class="col-md-6">
                <h6>电商相关文本：</h6>
                <ul class="list-unstyled">
                    <li><code>我想在淘宝上买一个很好的手机</code></li>
                    <li><code>京东商城的电商运营策略分析</code></li>
                    <li><code>拼多多的用户转化率和流量分析</code></li>
                    <li><code>支付宝和微信支付的市场份额</code></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>技术相关文本：</h6>
                <ul class="list-unstyled">
                    <li><code>人工智能在电商推荐系统中的应用</code></li>
                    <li><code>大数据分析用户行为和购买偏好</code></li>
                    <li><code>云计算技术支撑电商平台高并发</code></li>
                    <li><code>区块链技术在供应链管理中的创新</code></li>
                </ul>
            </div>
        </div>
    </div>
</div>
