﻿using FaoBao.HomePage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace FaoBao.HomePage.Pages;

public class DocumentSearchModel : PageModel
{
    private readonly DocumentService _documentService;

    public DocumentSearchModel(DocumentService documentService)
    {
        _documentService = documentService;
    }

    [BindProperty(SupportsGet = true)] public required string Keyword { get; set; }

    public SearchResult<SearchableDocument> SearchResult { get; set; } = new();

    public async Task OnGetAsync()
    {
        SearchResult = await _documentService.SearchDocumentsAsync(Keyword);
    }
}