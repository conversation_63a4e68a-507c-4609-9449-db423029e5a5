﻿/*--------------------------------------------------------------
# Document Center Styles
--------------------------------------------------------------*/

/* Document Center Container */
.document-center {
  min-height: 100vh;
  background: #f8fafc;
}



/* Main Content Layout */
.doc-main-content {
  display: flex;
  min-height: 100vh;
}

/* Left Sidebar */
.doc-sidebar {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  position: sticky;
  top: 0;
  height: 100vh;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.sidebar-toggle {
  display: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.sidebar-toggle:hover {
  background: #f3f4f6;
}

/* Category Selector */
.category-selector {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.category-selector .form-select {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

/* Navigation Menu */
.doc-nav {
  padding: 8px 0;
font-size: 14px;
  font-family: Chinese Quote,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji;
  background: #ffffff;
}

.directories-container {
  padding: 0;
}

/* Section Styles */

.nav-section-title {
  padding: 12px;
  font-size: 13px;
  color: #333333;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  margin: 0;
  display: flex;
  align-items: center;
  user-select: none;
}

.nav-section-title:hover {
  background: #f5f5f5;
}

.nav-section-title.active {
  background: #e6f3ff;
  color: #0066cc;
}

.nav-section-title .expand-icon {
  margin-right: 6px;
  font-size: 10px;
  transition: transform 0.2s ease;
  color: #666666;
  width: 12px;
  text-align: center;
}

.nav-section-title.expanded .expand-icon {
  transform: rotate(90deg);
}

.section-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.section-name {
  font-size: 13px;
  color: #333333;
  -webkit-font-smoothing: antialiased;
}

/* Navigation Links */
.nav-link {
  display: block;
  padding: 10px 12px;
  color: #555555;
  text-decoration: none;
  transition: all 0.2s ease;
  background: #ffffff;
  line-height: 1.6;
  user-select: none;
  -webkit-font-smoothing: antialiased;
}

.nav-link:hover {
  background: #f5f5f5;
  color: #333333;
}

.nav-link:focus {
  outline: none;
  box-shadow: inset 3px 0 0 #3182ce;
}

.nav-item.active > .nav-link {
  background: #e6f3ff;
  color: #0066cc;
}

.nav-content {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-title {
  flex: 1;
}

.nav-name {
  display: block;
  width: 100%;
  color: inherit;
  line-height: inherit;
}

/* Expand Icon */
.expand-icon {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.2s ease, color 0.2s ease;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  flex-shrink: 0;
}

.expand-icon:hover {
  color: #374151;
  background: #f3f4f6;
}

.nav-item.expanded > .nav-link .expand-icon {
  transform: rotate(90deg);
}

.nav-section-title .expand-icon {
  margin-right: 6px;
  font-size: 10px;
  transition: transform 0.2s ease;
  color: #666666;
  width: 12px;
  text-align: center;
}

.nav-section-title.expanded .expand-icon {
  transform: rotate(90deg);
}

/* Children Container */
.nav-children {
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: #ffffff;
}

.nav-children.collapsed {
  max-height: 0;
}

/* Level-specific styles */
.nav-item[data-level="0"] .nav-link {
  padding-left: 12px;
  color: #333333;
}

.nav-item[data-level="1"] .nav-link {
  padding-left: 32px;

  color: #555555;
}

.nav-item[data-level="2"] .nav-link {
  padding-left: 52px;
  color: #555555;
}

.nav-item[data-level="3"] .nav-link {
  padding-left: 72px;
  color: #555555;
}

.nav-item[data-level="4"] .nav-link {
  padding-left: 92px;
  color: #666666;
}

.nav-item.active .nav-name {
  font-weight: 700;
}
/* Scrollbar Styling */
.doc-sidebar::-webkit-scrollbar {
  width: 4px;
}

.doc-sidebar::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.doc-sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.doc-sidebar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading State */
.nav-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
}

.nav-loading .spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Right Content Area */
.doc-content {
  flex: 1;
  display: flex;
  background: #ffffff;
  position: relative;
}

/* Document Content */
.document-content {
  flex: 1;
  padding: 0;
  max-width: calc(100% - 240px);
  transition: max-width 0.3s ease;
}

.document-content.no-toc {
  max-width: 100%;
}

/* Document Header */
.document-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 32px 16px;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.document-title-section {
  max-width: 800px;
}

.title-row {
  margin-bottom: 12px;
}

.document-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
  line-height: 1.4;
}

.document-meta {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 13px;
  color: #6b7280;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.meta-item i {
  font-size: 13px;
  color: #9ca3af;
}

.document-description {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-body {
  width: 100%;
  line-height: 1.7;
  color: #374151;
  padding: 32px;
}

.content-container {
  width: 100%;
  max-width: 800px;
}

/* 当没有大纲时，内容容器占满整个宽度 */
.document-content.no-toc .content-container {
  max-width: none;
}

.content-container h1 {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 24px 0;
}

.content-container h2 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 32px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.content-container h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 24px 0 12px 0;
}

.content-container p {
  margin-bottom: 16px;
  font-size: 16px;
}

.content-container ul, .content-container ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.content-container li {
  margin-bottom: 8px;
}

.content-container code {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 14px;
  color: #e11d48;
}

.content-container pre {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin-bottom: 16px;
}

.content-container img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
  display: block;
}

.content-container figure {
  margin: 16px 0;
  text-align: center;
}

.content-container figure img {
  margin: 0 auto;
}

.content-container figcaption {
  font-size: 14px;
  color: #6b7280;
  margin-top: 8px;
  font-style: italic;
}

.content-container table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  font-size: 14px;
}

.content-container table th,
.content-container table td {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
  text-align: left;
}

.content-container table th {
  background: #f9fafb;
  font-weight: 600;
}

.content-container blockquote {
  border-left: 4px solid #3b82f6;
  background: #f8fafc;
  padding: 16px 20px;
  margin: 16px 0;
  font-style: italic;
  color: #4b5563;
}

.welcome-content {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.welcome-content h1 {
  color: #111827;
  margin-bottom: 16px;
}











/* Table of Contents */
.doc-toc {
  width: 240px;
  background: #ffffff;
  border-left: 1px solid #e5e7eb;
  position: absolute;
  top: 0;
  right: 0;
  height: 100vh;
  overflow-y: auto;
  flex-shrink: 0;
}

.toc-header {
  padding: 20px 16px 12px;
  border-bottom: 1px solid #f3f4f6;
  background: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.toc-header h4 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toc-header i {
  font-size: 14px;
  color: #6b7280;
}

.toc-content {
  padding: 8px 0 24px;
}

.toc-loading,
.toc-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 32px 16px;
  color: #9ca3af;
  font-size: 13px;
}

.toc-tree {
  list-style: none;
  margin: 0;
  padding: 0;
}

.toc-item {
  position: relative;
}

.toc-item-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toc-expand-icon {
  font-size: 10px;
  color: #9ca3af;
  cursor: pointer;
  transition: transform 0.2s ease;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.toc-expand-icon:hover {
  color: #6b7280;
}

.toc-expand-icon.expanded {
  transform: rotate(90deg);
}

.toc-link {
  display: block;
  padding: 6px 8px 6px 0;
  color: #6b7280;
  text-decoration: none;
  font-size: 13px;
  line-height: 1.4;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.toc-link:hover {
  color: #374151;
  font-weight: 600;
}

.toc-link.active {
  color: #1f2937;
  font-weight: 700;
}

.toc-children {
  margin-left: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.toc-children.collapsed {
  max-height: 0;
  opacity: 0;
}

.toc-children.expanded {
  max-height: 1000px;
  opacity: 1;
}

/* TOC Level Styles */
.toc-item[data-level="1"] > .toc-item-content {
  padding-left: 16px;
}

.toc-item[data-level="1"] .toc-link {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.toc-item[data-level="2"] > .toc-item-content {
  padding-left: 12px;
}

.toc-item[data-level="2"] .toc-link {
  font-size: 13px;
  color: #6b7280;
}

.toc-item[data-level="3"] > .toc-item-content {
  padding-left: 8px;
}

.toc-item[data-level="3"] .toc-link {
  font-size: 12px;
  color: #6b7280;
}

.toc-item[data-level="4"] > .toc-item-content {
  padding-left: 4px;
}

.toc-item[data-level="4"] .toc-link {
  font-size: 12px;
  color: #9ca3af;
}

.toc-item[data-level="5"] > .toc-item-content,
.toc-item[data-level="6"] > .toc-item-content {
  padding-left: 0;
}

.toc-item[data-level="5"] .toc-link,
.toc-item[data-level="6"] .toc-link {
  font-size: 11px;
  color: #9ca3af;
}

/* TOC Scrollbar */
.doc-toc::-webkit-scrollbar {
  width: 4px;
}

.doc-toc::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.doc-toc::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.doc-toc::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}



/* Responsive Design */
@media (max-width: 1200px) {
  .doc-toc {
    width: 240px;
  }

  .document-content {
    max-width: calc(100% - 240px);
  }
}

@media (max-width: 1024px) {
  .document-content {
    max-width: 100%;
  }

  .document-content.no-toc {
    max-width: 100%;
  }

  .doc-toc {
    display: none !important;
  }

  .content-container {
    max-width: none;
  }





@media (max-width: 768px) {
  .doc-sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    z-index: 200;
    transition: left 0.3s ease;
  }

  .doc-sidebar.open {
    left: 0;
  }

  .sidebar-toggle {
    display: block;
  }

  .doc-main-content {
    flex-direction: column;
  }

  .document-content {
    padding: 0;
  }

  .document-header {
    padding: 16px 20px 12px;
  }

  .document-title {
    font-size: 20px;
  }

  .document-meta {
    gap: 12px;
    font-size: 12px;
  }

  .meta-item {
    gap: 4px;
  }

  .document-description {
    max-width: 200px;
  }

  .content-body {
    padding: 20px;
  }

  .nav-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .content-body h1 {
    font-size: 24px;
  }

  .content-body h2 {
    font-size: 18px;
  }

  .content-body p {
    font-size: 14px;
  }
}
