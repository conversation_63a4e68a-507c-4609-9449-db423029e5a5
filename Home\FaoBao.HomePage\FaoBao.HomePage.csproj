<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aliyun.OSS.SDK.NetCore" Version="2.14.1" />
    <PackageReference Include="jieba.NET" Version="0.42.2" />
    <PackageReference Include="MeiliSearch" Version="0.16.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Resources\ecommerce_dict.txt" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="Resources\stopwords.txt" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>
