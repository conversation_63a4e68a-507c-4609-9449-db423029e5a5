﻿using System.Text.Json.Serialization;

namespace FaoBao.HomePage.Dtos.YuQue;

public class BaseUserDto
{
    [JsonPropertyName("id")]
    public long Id { get; set; }

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("login")]
    public string Login { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("avatar_url")]
    public string AvatarUrl { get; set; } = string.Empty;

    [JsonPropertyName("followers_count")]
    public int FollowersCount { get; set; }
    
    [JsonPropertyName("following_count")]
    public int FollowingCount { get; set; }

    [JsonPropertyName("public")]
    public int Public { get; set; }

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("created_at")]
    public string CreatedAt { get; set; } = string.Empty;

    [JsonPropertyName("updated_at")]
    public string UpdatedAt { get; set; } = string.Empty;

    [JsonPropertyName("work_id")]
    public string WorkId { get; set; } = string.Empty;

    [JsonPropertyName("organization_id")]
    public long OrganizationId { get; set; }

    [JsonPropertyName("_serializer")]
    public string Serializer { get; set; } = string.Empty;
}