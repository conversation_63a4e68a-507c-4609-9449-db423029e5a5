﻿@page "/products"
@model ProductModel
@{
    ViewData["Title"] = "产品列表 - 法宝";
}

@section Styles {
    <link href="/css/products.css" rel="stylesheet" />
}
@section Scripts {
    <script src="/js/products.js"></script>
}
<!-- Hero Section -->
<section id="products-hero" class="products-hero section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center" data-aos="fade-up">
                <h1>产品生态</h1>
                <p>全平台覆盖，一站式数字商品自动发货解决方案</p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">@Model.PageData.Stats.SupportedPlatforms</div>
                        <div class="stat-label">支持平台</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">@Model.PageData.Stats.ServedMerchants</div>
                        <div class="stat-label">服务商家</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">@Model.PageData.Stats.SuccessRate</div>
                        <div class="stat-label">发货成功率</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Platform Filter Section -->
<section class="platform-filter-section">
    <div class="container">
        <div class="filter-tabs" data-aos="fade-up">
            <button class="filter-tab active" data-filter="all">
                <i class="bi bi-grid"></i>
               全部服务
            </button>
            <button class="filter-tab" data-filter="ecommerce">
                <i class="bi bi-shop"></i>
                订单处理
            </button>
            <button class="filter-tab" data-filter="social">
                <i class="bi bi-camera-video"></i>
                配套周边
            </button>
            <button class="filter-tab" data-filter="live">
                <i class="bi bi-broadcast"></i>
                生态服务
            </button>
        </div>
    </div>
</section>

<!-- Products Grid Section -->
<section class="products-grid-section">
    <div class="container">
        <div class="products-grid" id="productsGrid">
            @foreach (var product in Model.PageData.Products.Where(p => p.IsActive).OrderBy(p => p.Order))
            {
                <div class="product-card show"
                     data-category="@product.Category"
                     data-aos="fade-up">
                    @if (product.IsPopular)
                    {
                        <div class="popular-badge">热门</div>
                    }

                    <div class="product-header">
                        <div class="product-logo @product.LogoClass">@product.Logo</div>
                        <div class="product-info">
                            <h3>@product.Name</h3>
                            <div class="product-category">@product.CategoryDisplay</div>
                        </div>
                    </div>

                    <div class="product-description">
                        @product.Description
                    </div>

                    <div class="product-features">
                        <h4>核心功能</h4>
                        <div class="features-list">
                            @foreach (var feature in product.Features)
                            {
                                <div class="feature-item">
                                    <i class="bi bi-check-circle-fill"></i>
                                    @feature
                                </div>
                            }
                        </div>
                    </div>

                    <div class="product-stats">
                        <div class="stat">
                            <div class="stat-value">@product.Stats.Users</div>
                            <div class="stat-label">服务商家</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">@product.Stats.Orders</div>
                            <div class="stat-label">处理订单</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">@product.Stats.SuccessRate</div>
                            <div class="stat-label">成功率</div>
                        </div>
                    </div>

                    <div class="product-actions">
                        <a href="/help?category=platform-guide&doc=@(product.Name.ToLower())-integration"
                           class="action-btn primary">
                            接入指南
                        </a>
                        <a href="#"
                           class="action-btn secondary"
                           onclick="showProductDemo('@product.Name.ToLower()')">
                            功能演示
                        </a>
                    </div>
                </div>
            }


        </div>
    </div>
</section>

<!-- Features Comparison Section -->
<section class="features-comparison-section">
    <div class="container">
        <div class="section-header" data-aos="fade-up">
            <h3>功能对比</h3>
            <p>不同平台支持的核心功能一览</p>
        </div>

        <div class="comparison-table-wrapper"
             data-aos="fade-up"
             data-aos-delay="100">
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能特性</th>
                        <th>淘宝/天猫</th>
                        <th>京东</th>
                        <th>拼多多</th>
                        <th>抖音</th>
                        <th>快手</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>自动发货</td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                    </tr>
                    <tr>
                        <td>订单同步</td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                    </tr>
                    <tr>
                        <td>库存管理</td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-x-circle-fill text-danger"></i></td>
                        <td><i class="bi bi-x-circle-fill text-danger"></i></td>
                    </tr>
                    <tr>
                        <td>批量操作</td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                    </tr>
                    <tr>
                        <td>数据统计</td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                        <td><i class="bi bi-check-circle-fill text-success"></i></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</section>

<!-- Integration Guide Section -->
<section class="integration-guide-section">
    <div class="container">
        <div class="section-header" data-aos="fade-up">
            <h3>快速接入</h3>
            <p>三步完成平台对接，开启自动化运营</p>
        </div>

        <div class="row">
            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
                <div class="guide-step">
                    <div class="step-number">1</div>
                    <div class="step-icon">
                        <i class="bi bi-person-plus"></i>
                    </div>
                    <h4>注册授权</h4>
                    <p>注册法宝账号，完成平台店铺授权，建立安全连接</p>
                </div>
            </div>

            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
                <div class="guide-step">
                    <div class="step-number">2</div>
                    <div class="step-icon">
                        <i class="bi bi-box"></i>
                    </div>
                    <h4>商品配置</h4>
                    <p>上传数字商品，设置发货规则，配置商品映射关系</p>
                </div>
            </div>

            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="300">
                <div class="guide-step">
                    <div class="step-number">3</div>
                    <div class="step-icon">
                        <i class="bi bi-rocket-takeoff"></i>
                    </div>
                    <h4>开始运营</h4>
                    <p>启动自动发货，实时监控订单，享受自动化运营</p>
                </div>
            </div>
        </div>
    </div>
</section>

