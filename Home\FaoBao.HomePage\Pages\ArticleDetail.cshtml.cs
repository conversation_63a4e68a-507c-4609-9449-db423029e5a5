﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using FaoBao.HomePage.Services;
using FaoBao.HomePage.Models;

namespace FaoBao.HomePage.Pages
{
    public class ArticleDetailModel : PageModel
    {
        private readonly ILogger<ArticleDetailModel> _logger;
        private readonly ContentService _contentService;

        public ArticleDetailModel(ILogger<ArticleDetailModel> logger, ContentService contentService)
        {
            _logger = logger;
            _contentService = contentService;
        }

        public ArticleDetail? Article { get; set; }
        public List<ArticleCategory> Categories { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string Slug { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync()
        {
            if (string.IsNullOrEmpty(Slug))
            {
                return NotFound();
            }

            try
            {
                Article = await _contentService.GetArticleDetailAsync(Slug);
                
                if (Article == null)
                {
                    return NotFound();
                }

                // 设置页面元数据
                ViewData["Title"] = $"{Article.Title} - 技术文章 - 法宝";
                ViewData["Description"] = !string.IsNullOrEmpty(Article.MetaDescription) 
                    ? Article.MetaDescription 
                    : Article.Summary;
                ViewData["Keywords"] = !string.IsNullOrEmpty(Article.MetaKeywords) 
                    ? Article.MetaKeywords 
                    : $"法宝,{Article.Category.Name},{string.Join(",", Article.Tags.Select(t => t.Name))}";

                // 获取分类列表用于导航
                Categories = await _contentService.GetArticleCategoriesAsync();

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文章详情时发生错误，slug: {Slug}", Slug);
                return NotFound();
            }
        }
    }
}
