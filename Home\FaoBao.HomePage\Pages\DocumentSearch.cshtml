﻿@page "/docs/search"
@model FaoBao.HomePage.Pages.DocumentSearchModel

@{
  ViewData["Title"] = $"与 {Model.Keyword} 有关的搜索结果 - 法宝";
  //ViewData["Description"] = 
  ViewData["ActiveMenu"] = "/Document";
}
@section Styles {
  <link href="/css/doc-search.css" rel="stylesheet" asp-append-version="true"/>
}
<!-- Compact Search Hero Section -->
<section id="search-hero" class="search-hero section">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8" data-aos="fade-up">
        <div class="search-box">
          <form method="get" asp-page="/DocumentSearch">
            <input
              type="text"
              name="keyword"
              placeholder="搜索文档、功能或问题..."
              value="@Model.Keyword"
              class="form-control"/>
            <button type="submit" class="search-btn">
              <i class="bi bi-search"></i>
            </button>
          </form>
         
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Search Results Section -->
<section id="search-results" class="search-results section">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <!-- Search Stats -->
        <div class="search-stats" data-aos="fade-up">
          <div class="stats-info">
            <span id="searchKeyword" class="search-keyword">“@Model.Keyword”</span>
            <span id="searchCount" class="search-count">找到 @Model.SearchResult.EstimatedTotalHits 个结果</span>
          </div>
          <div class="search-filters">
            <div class="filter-group">
              <label>排序方式：</label>
              <select class="form-select form-select-sm" id="sortBy">
                <option value="relevance">相关性</option>
                <option value="date">更新时间</option>
                <option value="title">标题</option>
              </select>
            </div>
            <div class="filter-group">
              <label>分类筛选：</label>
              <select class="form-select form-select-sm" id="categoryFilter">
                <option value="all">全部分类</option>
                <option value="getting-started">快速开始</option>
                <option value="platform-guide">平台对接</option>
                <option value="product-management">商品管理</option>
                <option value="order-management">订单管理</option>
                <option value="api-docs">API文档</option>
                <option value="troubleshooting">故障排除</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Search Results List -->
        <div id="searchResultsList" class="search-results-list" data-aos="fade-up" data-aos-delay="100">
          @if (Model.SearchResult.Hits.Any())
          {
            foreach (var document in Model.SearchResult.Hits)
            {
              <div class="search-result-item" onclick="openDocument('getting-started', 'quick-setup')">
                <div class="result-header">
                  <h3 class="result-title">@Html.Raw(document.Title)</h3>
                  <span class="result-category">@document.Type</span>
                </div>
                <p class="result-description">@Html.Raw(document.Content?.Substring(0,200))</p>
                <div class="result-meta">
                  <div class="result-date">
                    <i class="bi bi-calendar3"></i>
                    <span>更新于 @document.UpdatedAt</span>
                  </div>
                  <div class="result-actions">
                    <a href="#" class="result-link" onclick="event.stopPropagation(); openDocument('getting-started', 'quick-setup')">
                      查看详情 <i class="bi bi-arrow-right"></i>
                    </a>
                  </div>
                </div>
              </div>
            } 
          }
          else
          {
            <div class="no-results">
              <i class="bi bi-search"></i>
              <h3>未找到相关结果</h3>
              <p>请输入搜索关键词</p>
              <div class="suggestions">
                <h4>搜索建议：</h4>
                <ul>
                  <li>检查关键词拼写是否正确</li>
                  <li>尝试使用更通用的关键词</li>
                  <li>减少关键词数量</li>
                  <li>尝试使用同义词</li>
                </ul>
              </div>
            </div>
          }
        </div>

        <!-- Pagination -->
        <div class="search-pagination" data-aos="fade-up" data-aos-delay="200">
          <nav aria-label="搜索结果分页">
            <ul class="pagination justify-content-center">
              <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1">上一页</a>
              </li>
              <li class="page-item active">
                <a class="page-link" href="#">1</a>
              </li>
              <li class="page-item">
                <a class="page-link" href="#">2</a>
              </li>
              <li class="page-item">
                <a class="page-link" href="#">3</a>
              </li>
              <li class="page-item">
                <a class="page-link" href="#">下一页</a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>
</section>