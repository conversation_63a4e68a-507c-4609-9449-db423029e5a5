using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using FaoBao.HomePage.Services;
using FaoBao.HomePage.Dtos.Document;

namespace FaoBao.HomePage.Pages
{
    public class DocumentModel : PageModel
    {
        private readonly ILogger<DocumentModel> _logger;
        private readonly DocumentService _documentService;

        public DocumentModel(
            ILogger<DocumentModel> logger,
            DocumentService documentService)
        {
            _logger = logger;
            _documentService = documentService;
        }

        public List<CategoriesDto>? Categories { get; set; }
        public List<DocumentListDto>? CurrentDocuments { get; set; }
        public CategoriesDto CurrentCategory { get; set; }

        public async Task OnGetAsync(string? slug = null)
        {
            try
            {
                Categories = await _documentService.Categories();
                // 设置active状态
                if (Categories != null && Categories.Any())
                {
                    SetActiveCategoryStatus(Categories, slug);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取帮助页面数据时发生错误");
                // 使用空数据作为后备
                Categories = new List<CategoriesDto>();
            }

            CurrentDocuments = await _documentService.DocumentsAsync(CurrentCategory.Slug);
        }


        /// <summary>
        /// 根据分类和关键词搜索文档
        /// </summary>
        public async Task<IActionResult> OnGetSearchWithCategoryAsync(string? categorySlug, string? keyword)
        {
            try
            {
                var results = await SearchDocumentsAsync(categorySlug, keyword);
                return new JsonResult(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索文档时发生错误: CategorySlug={CategorySlug}, Keyword={Keyword}", categorySlug,
                    keyword);
                return new JsonResult(new List<object>());
            }
        }

        /// <summary>
        /// 搜索文档的核心逻辑
        /// </summary>
        private async Task<List<object>> SearchDocumentsAsync(string? categorySlug, string? keyword)
        {
            var results = new List<object>();

            // 如果没有提供任何搜索条件，返回空结果
            if (string.IsNullOrWhiteSpace(categorySlug) && string.IsNullOrWhiteSpace(keyword))
            {
                return results;
            }

            // 获取所有分类
            var categories = await _documentService.Categories();
            if (categories == null || !categories.Any())
            {
                return results;
            }

            // 确定要搜索的分类
            var categoriesToSearch = new List<CategoriesDto>();
            if (!string.IsNullOrWhiteSpace(categorySlug))
            {
                var selectedCategory =
                    categories.FirstOrDefault(c => c.Slug.Equals(categorySlug, StringComparison.OrdinalIgnoreCase));
                if (selectedCategory != null)
                {
                    categoriesToSearch.Add(selectedCategory);
                }
            }
            else
            {
                // 如果没有指定分类，搜索所有分类
                categoriesToSearch.AddRange(categories);
            }

            // 在选定的分类中搜索文档
            foreach (var category in categoriesToSearch)
            {
                try
                {
                    var documents = await _documentService.DocumentsAsync(category.Slug);
                    if (documents == null) continue;

                    var filteredDocs = documents.AsEnumerable();

                    // 如果提供了关键词，进行关键词过滤
                    if (!string.IsNullOrWhiteSpace(keyword))
                    {
                        filteredDocs = filteredDocs.Where(d =>
                            d.Title.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                            d.Description.Contains(keyword, StringComparison.OrdinalIgnoreCase));
                    }

                    // 将结果添加到列表中
                    foreach (var doc in filteredDocs)
                    {
                        results.Add(new
                        {
                            id = doc.Id,
                            title = doc.Title,
                            description = doc.Description,
                            slug = doc.Slug,
                            updateTime = doc.UpdateTime,
                            categoryName = category.Name,
                            categorySlug = category.Slug,
                            url = $"/Detail/{category.Slug}/{doc.Slug}"
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "搜索分类 {CategorySlug} 时发生错误", category.Slug);
                    continue;
                }
            }

            return results.Take(20).ToList(); // 限制结果数量
        }


        /// <summary>
        /// 设置分类的active状态
        /// </summary>
        private void SetActiveCategoryStatus(List<CategoriesDto> categories, string? slug)
        {
            // 如果没有指定categoryId，使用Order=1的分类
            if (string.IsNullOrEmpty(slug))
            {
                var defaultCategory = categories.FirstOrDefault(c => c.Order == 1);
                if (defaultCategory != null)
                {
                    slug = defaultCategory.Slug;
                    CurrentCategory = defaultCategory;
                }
            }

            // 设置active状态
            foreach (var category in categories)
            {
                if (category.Slug == slug)
                {
                    CurrentCategory = category;
                    category.IsActive = true;
                }
                else
                {
                    category.IsActive = false;
                }
            }
        }

        /// <summary>
        /// 标记活动的导航项
        /// </summary>
        private void MarkActiveDirectoryItem(List<DirectoriesDto> directories, string slug)
        {
            foreach (var item in directories)
            {
                if (item.Slug == slug)
                {
                    item.IsActive = true;
                    _logger.LogDebug("标记导航项为活动状态: {Title} (slug: {Slug})", item.Title, slug);
                }
                else
                {
                    item.IsActive = false;
                }

                // 递归处理子项
                if (item.Children?.Any() == true)
                {
                    MarkActiveDirectoryItem(item.Children, slug);
                }
            }
        }
    }
}