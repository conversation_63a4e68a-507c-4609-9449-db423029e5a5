using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using FaoBao.HomePage.Services;
using FaoBao.HomePage.Models;
using FaoBao.HomePage.Dtos.Document;
using FaoBao.HomePage.Dtos.YuQue;

namespace FaoBao.HomePage.Pages
{
    public class DocumentDetailModel : PageModel
    {
        private readonly ILogger<DocumentModel> _logger;
        private readonly ContentService _contentService;
        private readonly DocumentService _documentService;


        public DocumentDetailModel(ILogger<DocumentModel> logger, ContentService contentService,
            DocumentService documentService)
        {
            _logger = logger;
            _contentService = contentService;
            _documentService = documentService;
        }


        public List<CategoriesDto>? Categories { get; set; }
        public List<DirectoriesDto>? Directories { get; set; } = new();
        public DocumentDetailDto? CurrentDocument { get; set; }
        public string? CurrentSlug { get; set; }
        public string? CurrentBook { get; set; }

        public async Task OnGetAsync(string? book=null, string? slug = null)
        {
            CurrentSlug = slug;
            CurrentBook = book;

            try
            {
                Categories = await _documentService.Categories();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取帮助页面数据时发生错误");
                // 使用空数据作为后备
                Categories = new List<CategoriesDto>();
            }

            //如果有book参数，获取book的目录
            if (!string.IsNullOrEmpty(book))
            {
                try
                {
                    // 获取目录数据
                    Directories = await _documentService.DirectoriesAsync(book);

                    // 如果有slug参数，标记对应的导航项为活动状态
                    if (!string.IsNullOrEmpty(slug))
                    {
                        MarkActiveDirectoryItem(Directories, slug);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取目录数据时发生错误");
                    Directories = new List<DirectoriesDto>();
                }

                // 如果有slug参数，获取对应的文档内容
                if (!string.IsNullOrEmpty(slug))
                {
                    try
                    {
                        CurrentDocument = await _documentService.DocumentDetailAsync("tz7e98", book, slug);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "获取文档内容时发生错误，slug: {Slug}", slug);
                    }
                }
            }
        }

        

      
        /// <summary>
        /// 标记活动的导航项
        /// </summary>
        private void MarkActiveDirectoryItem(List<DirectoriesDto> directories, string slug)
        {
            foreach (var item in directories)
            {
                if (item.Slug == slug)
                {
                    item.IsActive = true;
                    _logger.LogDebug("标记导航项为活动状态: {Title} (slug: {Slug})", item.Title, slug);
                }
                else
                {
                    item.IsActive = false;
                }

                // 递归处理子项
                if (item.Children?.Any() == true)
                {
                    MarkActiveDirectoryItem(item.Children, slug);
                }
            }
        }
    }
}