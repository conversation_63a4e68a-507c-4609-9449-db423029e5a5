﻿using System.ComponentModel;
using System.Text.Json;
using System.Text.Json.Serialization;
using FaoBao.HomePage.Common;
using FaoBao.HomePage.Dtos.YuQue;
using FaoBao.HomePage.Dtos.YuQue.Responses;

namespace FaoBao.HomePage.Services;

public class YuQueService
{
    private readonly ILogger<YuQueService> _logger;
    private readonly HttpClient _client;


    private readonly JsonSerializerOptions _options = new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = true,
       
         Converters = { new Common.DateTimeOffsetConverter() ,new FlexibleLongConverter()},
         DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
         WriteIndented = true
    };

    public YuQueService(
        IHttpClientFactory httpClientFactory,
        ILogger<YuQueService> logger
      )
    {
        _logger = logger;
        _client = httpClientFactory.CreateClient("yuque");

    }

    public async Task<ReposResponse?> GetReposAsync(string login)
    {
        var resp = await _client.GetAsync($"groups/{login}/repos?offset=0&limit=100&type=Book");
        if (resp.StatusCode != System.Net.HttpStatusCode.OK)
            return null;
        var content = await resp.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<ReposResponse>(content, _options);
    }

    public async Task<DocsResponse?> GetDocsAsync(string groupLogin, string bookSlug)
    {
        try
        {
            var resp = await _client.GetAsync($"repos/{groupLogin}/{bookSlug}/docs");
            if (resp.StatusCode != System.Net.HttpStatusCode.OK)
                return null;
            var content = await resp.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<DocsResponse>(content,_options);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "GetDocsAsync 发生异常");
            return null;
        }
    }

    public async Task<TocResponse?> GetDirectoriesAsync(string groupLogin, string bookSlug)
    {
        try
        {
            var resp = await _client.GetAsync($"repos/{groupLogin}/{bookSlug}/toc");
            if (resp.StatusCode != System.Net.HttpStatusCode.OK)
                return null;
            var content = await resp.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<TocResponse>(content,_options);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "GetDocsAsync 发生异常");
            return null;
        }
    }

    public async Task<DocDetailResponse?> GetDocumentAsync(string groupLogin, string bookSlug, string id)
    {
        try
        {
          
            // 缓存不存在，调用 API
            var resp = await _client.GetAsync($"repos/{groupLogin}/{bookSlug}/docs/{id}");
            if (resp.StatusCode != System.Net.HttpStatusCode.OK)
                return null;

            var content = await resp.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<DocDetailResponse>(content, _options);

           
            return result;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "GetDocumentAsync 发生异常");
            return null;
        }
    }

  
}