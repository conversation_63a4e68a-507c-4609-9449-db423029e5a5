﻿<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>@ViewData["Title"]</title>
    <meta name="description" content="@(ViewData["Description"] ?? "为淘宝、天猫、京东、拼多多、抖音、快手、闲鱼、小红书、微店、有赞等电商平台的商家提供数字类产品的自动发货解决方案")" />
    <meta name="keywords" content="@(ViewData["Keywords"] ?? "法宝,自动发货,淘宝,天猫,京东,拼多多,抖音,快手,闲鱼,小红书,微店,有赞,电商")" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="@(Context.Request.Scheme)://@(Context.Request.Host)@(Context.Request.Path)">
    <meta property="og:title" content="@ViewData["Title"]">
    <meta property="og:description" content="@(ViewData["Description"] ?? "为淘宝、天猫、京东、拼多多、抖音、快手、闲鱼、小红书、微店、有赞等电商平台的商家提供数字类产品的自动发货解决方案")">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="@(Context.Request.Scheme)://@(Context.Request.Host)@(Context.Request.Path)">
    <meta property="twitter:title" content="@ViewData["Title"]">
    <meta property="twitter:description" content="@(ViewData["Description"] ?? "为淘宝、天猫、京东、拼多多、抖音、快手、闲鱼、小红书、微店、有赞等电商平台的商家提供数字类产品的自动发货解决方案")">

    <!-- Canonical URL -->
    <link rel="canonical" href="@(Context.Request.Scheme)://@(Context.Request.Host)@(Context.Request.Path)" />
    <!-- Favicons -->
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <!-- Vendor CSS Files -->
    <link href="/vendor/bootstrap/css/bootstrap.min.css"
          rel="stylesheet" />
    <link href="/vendor/bootstrap-icons/bootstrap-icons.css"
          rel="stylesheet" />
    <link href="/vendor/aos/aos.css" rel="stylesheet" />
    <link href="/vendor/swiper/swiper-bundle.min.css" rel="stylesheet" />
    <link href="/css/main.css" rel="stylesheet" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>

<body class="index-page">
    <header id="header" class="header d-flex align-items-center fixed-top">
        <div class="container position-relative d-flex align-items-center justify-content-between">
            <a href="#" class="logo d-flex align-items-center me-auto me-xl-0">
                <!-- Uncomment the line below if you also wish to use an image logo -->
                <svg viewBox="0 0 67.7 18.22">
                    <g fill="#fff">
                        <path d="M19.33 8.65c1.02-.91 1.67-2.29 1.64-3.83-.05-2.67-2.19-4.77-4.7-4.77h-5.78v1.64c0 .7.36 1.37.96 **********.**********.89.71 1.47 1.83 1.47 3.11 0 .49-.09.96-.25 1.39-.22.6-.57 1.13-1.02 1.54-.23.21-.48.39-.75.53 2.34.75 4.05 3.05 4.05 5.79v.2h8.03c.06-.36.09-.73.09-1.11 0-3.03-1.83-5.6-4.37-6.56M0 16.32h4.23v-.21c0-2.09 1-3.94 2.52-5.02.47-.33.98-.59 1.53-.77v-.79h-1c-.2-.23-.38-.48-.53-.76-.3-.57-.48-1.22-.48-1.92 0-1.54.85-2.87 2.08-3.5.62-.31.98-1 .98-1.73V.04H0v16.28zM0 17.04v1.18h4.58c-.13-.38-.23-.77-.29-1.18H0zM15.51 17.04c-.06.41-.16.81-.29 1.18h7.82c.17-.38.31-.77.42-1.18h-7.95z" />
                    </g>
                    <g fill="#fff">
                        <path d="M29.08.92h4v2.24h-4zM65.45 1.61h-5.84V.16h-2.24v1.45h-5.82v-.45h-2.24v4.32h2.24V3.85h13.9v1.63h2.25V1.61h-2.25zM65.27 15.31h-5.66v-2.63h7.58v-1.84h-7.58V8.3h7.88V6.46h-18V8.3h7.88v2.54h-7.58v1.84h7.58v2.63h-7.88v2.24h18.03V14h-2.25v1.31zM29.08 7.58h4v2.24h-4zM29.08 15.03h4v2.24h-4zM44.14 11.88l.76 3.43h-7.13l1.3-5.89h8.41V7.17H42V3.91h5.24V1.66H42V0h-2.25v1.66H34.5v2.25h5.25v3.26h-5.48v2.25h2.46l-1.8 8.13H47.74l-1.26-5.67h-2.34z" />
                    </g>
                </svg>
            </a>

            <nav id="navmenu" class="navmenu">
                <ul>
                    <li><menu asp-page="/Index" >首页</menu></li>
                    <li><menu asp-page="/Product">产品列表</menu></li>
                    <li><menu asp-page="/Document">文档中心</menu></li>
                    <li><menu asp-page="/Articles">新闻动态</menu></li>
                </ul>
                <button class="mobile-nav-toggle d-xl-none"
                        aria-label="Toggle navigation">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </nav>

            <div class="header-social-links">
                <a class="btn-login" href="https://console.fabao.com">登录控制台</a>
            </div>
        </div>
    </header>

    <main class="main">
        @RenderBody()
    </main>

    <footer id="footer" class="footer">
        <div class="container copyright text-center">
            <p>
                © <span>Copyright</span>
                <strong class="px-1 sitename">长沙齐发发网络科技有限公司</strong>
                <span>All Rights Reserved</span>
            </p>
            <div class="credits">
                <a href="https://beian.miit.gov.cn" target="_blank">湘ICP备2024098652号-2</a>
                <!-- ·<a href="https://beian.miit.gov.cn" target="_blank"
                  >湘公网安备50011202504348</a
                > -->
            </div>
        </div>
    </footer>

    <!-- Scroll Top -->
    <a href="#"
       id="scroll-top"
       class="scroll-top d-flex align-items-center justify-content-center">
        <i class="bi bi-arrow-up-short"></i>
    </a>

    <!-- Preloader -->
    <div id="preloader"></div>
    <script src="/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/vendor/php-email-form/validate.js"></script>
    <script src="/vendor/aos/aos.js"></script>
    <script src="/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="/vendor/imagesloaded/imagesloaded.pkgd.min.js"></script>
    <script src="/vendor/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="/js/main.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
