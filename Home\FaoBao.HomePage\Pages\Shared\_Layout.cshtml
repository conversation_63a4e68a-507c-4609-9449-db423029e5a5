﻿<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>@ViewData["Title"]</title>
    <meta name="description" content="@(ViewData["Description"] ?? "为淘宝、天猫、京东、拼多多、抖音、快手、闲鱼、小红书、微店、有赞等电商平台的商家提供数字类产品的自动发货解决方案")" />
    <meta name="keywords" content="@(ViewData["Keywords"] ?? "法宝,自动发货,淘宝,天猫,京东,拼多多,抖音,快手,闲鱼,小红书,微店,有赞,电商")" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="@(Context.Request.Scheme)://@(Context.Request.Host)@(Context.Request.Path)">
    <meta property="og:title" content="@ViewData["Title"]">
    <meta property="og:description" content="@(ViewData["Description"] ?? "为淘宝、天猫、京东、拼多多、抖音、快手、闲鱼、小红书、微店、有赞等电商平台的商家提供数字类产品的自动发货解决方案")">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="@(Context.Request.Scheme)://@(Context.Request.Host)@(Context.Request.Path)">
    <meta property="twitter:title" content="@ViewData["Title"]">
    <meta property="twitter:description" content="@(ViewData["Description"] ?? "为淘宝、天猫、京东、拼多多、抖音、快手、闲鱼、小红书、微店、有赞等电商平台的商家提供数字类产品的自动发货解决方案")">

    <!-- Canonical URL -->
    <link rel="canonical" href="@(Context.Request.Scheme)://@(Context.Request.Host)@(Context.Request.Path)" />
    <!-- Favicons -->
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <!-- Vendor CSS Files -->
    <link href="/vendor/bootstrap/css/bootstrap.min.css"
          rel="stylesheet" />
    <link href="/vendor/bootstrap-icons/bootstrap-icons.css"
          rel="stylesheet" />
    <link href="/vendor/aos/aos.css" rel="stylesheet" />
    <link href="/vendor/swiper/swiper-bundle.min.css" rel="stylesheet" />
    <link href="/css/main.css" rel="stylesheet" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>

<body class="index-page">
    <header id="header" class="header">
        <div class="container">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="bi bi-lightning-charge"></i>
                </div>
                <span class="logo-text">法宝</span>
            </a>

            <nav id="navmenu" class="navmenu">
                <ul>
                    <li><a href="/" class="@(ViewContext.RouteData.Values["page"]?.ToString() == "/Index" ? "active" : "")">首页</a></li>
                    <li class="dropdown">
                        <a href="#"><span>产品</span> <i class="bi bi-chevron-down"></i></a>
                        <ul>
                            <li><a href="/products">产品列表</a></li>
                            <li><a href="/products#integration">接入指南</a></li>
                        </ul>
                    </li>
                    <li><a href="/docs" class="@(ViewContext.RouteData.Values["page"]?.ToString() == "/Document" ? "active" : "")">解决方案</a></li>
                    <li><a href="/pricing">价格</a></li>
                    <li><a href="/docs" class="@(ViewContext.RouteData.Values["page"]?.ToString() == "/Document" ? "active" : "")">文档</a></li>
                    <li class="dropdown">
                        <a href="#"><span>更多</span> <i class="bi bi-chevron-down"></i></a>
                        <ul>
                            <li><a href="/articles">新闻动态</a></li>
                            <li><a href="/about">关于我们</a></li>
                            <li><a href="/contact">联系我们</a></li>
                        </ul>
                    </li>
                </ul>
                <button class="mobile-nav-toggle d-xl-none" aria-label="Toggle navigation">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </nav>

            <div class="header-actions">
                <a class="btn-login" href="https://console.fabao.com">登录控制台</a>
            </div>
        </div>
    </header>

    <main class="main">
        @RenderBody()
    </main>

    <footer id="footer" class="footer">
        <div class="container copyright text-center">
            <p>
                © <span>Copyright</span>
                <strong class="px-1 sitename">长沙齐发发网络科技有限公司</strong>
                <span>All Rights Reserved</span>
            </p>
            <div class="credits">
                <a href="https://beian.miit.gov.cn" target="_blank">湘ICP备2024098652号-2</a>
                <!-- ·<a href="https://beian.miit.gov.cn" target="_blank"
                  >湘公网安备50011202504348</a
                > -->
            </div>
        </div>
    </footer>

    <!-- Scroll Top -->
    <a href="#"
       id="scroll-top"
       class="scroll-top d-flex align-items-center justify-content-center">
        <i class="bi bi-arrow-up-short"></i>
    </a>

    <!-- Preloader -->
    <div id="preloader"></div>
    <script src="/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/vendor/php-email-form/validate.js"></script>
    <script src="/vendor/aos/aos.js"></script>
    <script src="/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="/vendor/imagesloaded/imagesloaded.pkgd.min.js"></script>
    <script src="/vendor/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="/js/main.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
