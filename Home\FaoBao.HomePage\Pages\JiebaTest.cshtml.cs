﻿using FaoBao.HomePage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace FaoBao.HomePage.Pages;

public class JiebaTestModel : PageModel
{
    private readonly JiebaService _jiebaService;

    public JiebaTestModel(JiebaService jiebaService)
    {
        _jiebaService = jiebaService;
    }

    [BindProperty]
    public string TestText { get; set; } = string.Empty;

    public List<string> SegmentsWithStopWords { get; set; } = new();
    public List<string> SegmentsWithoutStopWords { get; set; } = new();
    public string SearchKeywords { get; set; } = string.Empty;
    public string OptimizedQuery { get; set; } = string.Empty;

    public void OnGet()
    {
        // 默认测试文本
        TestText = "我想在淘宝上买一个很好的手机";
        PerformSegmentation();
    }

    public void OnPost()
    {
        if (!string.IsNullOrEmpty(TestText))
        {
            PerformSegmentation();
        }
    }

    private void PerformSegmentation()
    {
        if (string.IsNullOrEmpty(TestText))
            return;

        try
        {
            // 分词（包含停用词）
            SegmentsWithStopWords = _jiebaService.SegmentText(TestText, filterStopWords: false);

            // 分词（过滤停用词）
            SegmentsWithoutStopWords = _jiebaService.SegmentText(TestText, filterStopWords: true);

            // 生成搜索关键词
            SearchKeywords = _jiebaService.GenerateSearchKeywords(TestText, "");

            // 优化搜索查询
            OptimizedQuery = _jiebaService.OptimizeSearchQuery(TestText);
        }
        catch (Exception ex)
        {
            // 如果分词失败，显示错误信息
            SegmentsWithStopWords = new List<string> { $"分词失败: {ex.Message}" };
            SegmentsWithoutStopWords = new List<string>();
            SearchKeywords = $"错误: {ex.Message}";
            OptimizedQuery = $"错误: {ex.Message}";
        }
    }
}
