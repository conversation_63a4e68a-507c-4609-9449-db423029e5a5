﻿# 法宝HomePage数据说明

## 概述

本项目使用JSON文件来存储网站的动态内容，包括首页内容、产品信息和帮助文档。所有数据都存储在 `Data` 目录下的JSON文件中。

## 文件结构

```
Data/
├── homepage.json        # 首页数据（优势、服务、场景、FAQ）
├── products.json        # 产品页数据（统计、产品列表）
├── help.json           # 帮助文档数据
└── README.md           # 本说明文件
```

## 数据文件说明

### homepage.json

包含首页相关数据：

- **advantages**: 首页优势列表
- **services**: 首页服务列表
- **scenes**: 应用场景列表
- **faqs**: 常见问题列表

### products.json

包含产品页相关数据：

- **siteStats**: 网站统计数据
- **categories**: 产品分类列表
- **products**: 产品列表

### help.json

包含帮助文档数据：

- **categories**: 帮助分类列表
  - **documents**: 每个分类下的文档列表

## 数据管理

### 编辑数据

直接编辑对应的JSON文件：

1. **首页内容**：编辑 `homepage.json`
2. **产品信息**：编辑 `products.json`
3. **帮助文档**：编辑 `help.json`

编辑步骤：
1. 使用文本编辑器打开对应的JSON文件
2. 修改内容（注意保持JSON格式正确）
3. 保存文件
4. 重启应用或等待缓存刷新

## 数据字段说明

### 通用字段

- `id`: 唯一标识符
- `title`: 标题
- `description`: 描述
- `order`: 排序值（数字越小越靠前）
- `isActive`: 是否激活（true/false）

### 图标字段

使用Bootstrap Icons类名，例如：
- `bi-lightning-charge`: 闪电图标
- `bi-shield-check`: 盾牌图标
- `bi-graph-up`: 图表图标

### 产品特殊字段

- `logo`: 产品Logo文字
- `logoClass`: CSS类名（用于样式）
- `category`: 产品分类（ecommerce, social, live等）
- `features`: 功能特性数组
- `stats`: 统计数据对象

## 数据缓存

系统提供文件缓存功能：

- 自动检测文件修改时间
- 只有文件更新时才重新读取
- 提高页面加载性能

## 注意事项

1. **JSON格式**: 确保JSON格式正确，否则会导致页面显示异常
2. **字符编码**: 使用UTF-8编码保存文件
3. **备份**: 修改前建议先手动备份数据文件
4. **缓存**: 修改后可能需要等待缓存刷新才能看到效果
5. **权限**: 确保应用有读取Data目录的权限

## 示例数据

### 添加新的优势项目

```json
{
  "id": 5,
  "icon": "bi-star",
  "title": "新优势",
  "description": "这是一个新的优势描述",
  "order": 5,
  "isActive": true
}
```

### 添加新的产品

```json
{
  "id": 7,
  "name": "新平台",
  "logo": "新",
  "logoClass": "new-platform",
  "category": "ecommerce",
  "categoryDisplay": "电商平台",
  "description": "新平台的描述",
  "features": ["功能1", "功能2", "功能3"],
  "stats": {
    "users": "10万+",
    "orders": "50万+",
    "successRate": "99.0%"
  },
  "isPopular": false,
  "order": 7,
  "isActive": true
}
```

### 添加新的帮助文档

```json
{
  "id": "new-doc",
  "title": "新文档标题",
  "description": "新文档的描述",
  "icon": "bi-file-text",
  "categoryId": "getting-started",
  "updateTime": "2024-01-15T00:00:00",
  "content": "<h2>文档标题</h2><p>文档内容...</p>",
  "order": 3,
  "isActive": true
}
```

## 故障排除

### 常见问题

1. **页面显示空白**: 检查JSON格式是否正确
2. **数据不更新**: 清除浏览器缓存或重启应用
3. **保存失败**: 检查文件权限和磁盘空间
4. **图标不显示**: 确认图标类名是否正确

### 恢复数据

如果数据损坏，可以：

1. 从备份目录恢复历史版本
2. 使用管理界面的恢复功能
3. 手动复制备份文件覆盖当前文件

## 技术支持

如有问题，请联系技术支持团队或查看应用日志文件。
