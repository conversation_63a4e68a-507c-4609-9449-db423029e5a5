﻿@page "/docs/detail/{book?}/{slug?}"

@model FaoBao.HomePage.Pages.DocumentDetailModel
@{
    ViewData["Title"] = !string.IsNullOrEmpty(Model.CurrentDocument?.Title)
        ? $"{Model.CurrentDocument.Title} - 文档中心 - 法宝"
        : "文档中心 - 法宝";

    ViewData["Description"] = !string.IsNullOrEmpty(Model.CurrentDocument?.Description)
        ? Model.CurrentDocument.Description
        : "法宝文档中心，提供完整的开发指南和API文档";
    ViewData["ActiveMenu"] = "/Document";
}

@section Styles {
    <link href="/css/doc-detail.css" rel="stylesheet" asp-append-version="true"/>
    <link href="//editor.yuque.com/ne-editor/lake-content-v1.css" rel="stylesheet"/>
}

@section Scripts {
    <script src="/js/detail.js"></script>
}

<!-- Document Center Layout -->
<div class="document-center">

    <!-- Main Content Area -->
    <div class="doc-main-content">
        <!-- Left Sidebar -->
        <div class="doc-sidebar" style="@(string.IsNullOrEmpty(Model.CurrentBook) ? "display: none;" : "")">
            <!-- Category Selector -->
            <div class="category-selector">
                <select class="form-select" id="categorySelect">
                    <option value="">选择分类</option>
                    @foreach (var category in Model.Categories)
                    {
                        <option value="@category.Slug"
                                selected="@(category.Slug == Model.CurrentBook)">@category.Name</option>
                    }
                </select>
            </div>

            <!-- Navigation Menu -->
            <nav class="doc-nav">
                @{
                    async Task RenderDirectoryItem(Dtos.Document.DirectoriesDto item, int level = 0)
                    {
                        var activeClass = item.IsActive ? "active" : "";
                        if (item.Type == "TITLE")
                        {
                            <div class="nav-section expanded" data-level="@level" data-uuid="@item.Uuid">
                                <div class="nav-section-title expanded" data-toggle="section">
                                    @if (item.HasChildren)
                                    {
                                        <i class="expand-icon bi bi-chevron-right"></i>
                                    }
                                    <div class="section-content">
                                        <span class="section-name">@item.Title</span>
                                    </div>
                                </div>
                                @if (item.HasChildren)
                                {
                                    <div class="nav-children expanded" data-parent="@item.Uuid">
                                        @foreach (var child in item.Children)
                                        {
                                            await RenderDirectoryItem(child, level + 1);
                                        }
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="nav-item @activeClass" data-level="@level" data-uuid="@item.Uuid"
                                 title="Level: @level">
                                @if (item.Type == "DOC")
                                {
                                    <a asp-page="/DocumentDetail"
                                       asp-route-book="@Model.CurrentBook"
                                       asp-route-slug="@item.Slug" class="nav-link">
                                        <div class="nav-content">
                                            <div class="nav-title">
                                                <span class="nav-name">@item.Title</span>
                                            </div>
                                        </div>
                                    </a>
                                }
                                else
                                {
                                    <div class="nav-link">
                                        <div class="nav-content">
                                            @if (item.HasChildren)
                                            {
                                                <i class="expand-icon bi bi-chevron-right"></i>
                                            }
                                            <div class="nav-title">
                                                <span class="nav-name">@item.Title</span>
                                            </div>
                                        </div>
                                    </div>
                                }

                                @if (item.HasChildren)
                                {
                                    <div class="nav-children expanded" data-parent="@item.Uuid">
                                        @foreach (var child in item.Children)
                                        {
                                            await RenderDirectoryItem(child, level + 1);
                                        }
                                    </div>
                                }
                            </div>
                        }
                    }
                }

                @if (Model.Directories != null)
                {
                    <div class="directories-container">
                        @foreach (var rootItem in Model.Directories)
                        {
                            await RenderDirectoryItem(rootItem);
                        }
                    </div>
                }
            </nav>
        </div>

        <!-- Right Content Area -->
        <div class="doc-content">
            <!-- Document Content -->
            <div class="document-content" id="documentContent">
                <!-- Document Header -->
                @if (Model.CurrentDocument != null)
                {
                    <div class="document-header">
                        <div class="document-title-section">
                            <div class="title-row">
                                <h1 class="document-title">@Model.CurrentDocument.Title</h1>
                            </div>
                            <div class="document-meta">
                                <div class="meta-item">
                                    <i class="bi bi-clock-history"></i>
                                    <span>@(Model.CurrentDocument.UpdateTime?.ToString("yyyy-MM-dd HH:mm") ?? "未知时间")</span>
                                </div>
                                <div class="meta-item">
                                    <i class="bi bi-eye"></i>
                                    <span class="reading-time">阅读时间约 1 分钟</span>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <div class="content-body">
                    <div class="content-container">
                        @if (Model.CurrentDocument != null)
                        {
                            @Html.Raw(Model.CurrentDocument.BodyHtml)
                        }
                    </div>
                </div>
            </div>

            <!-- Table of Contents (Right Sidebar) -->
            <div class="doc-toc" id="docToc" style="@(string.IsNullOrEmpty(Model.CurrentBook) ? "display: none;" : "")">
                <div class="toc-header">
                    <h4>
                        <i class="bi bi-list-ul"></i>
                        大纲
                    </h4>
                </div>
                <div class="toc-content">
                    <div class="toc-loading">
                        <i class="bi bi-hourglass-split"></i>
                        <span>正在生成大纲...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
