﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using FaoBao.HomePage.Services;
using FaoBao.HomePage.Models;

namespace FaoBao.HomePage.Pages
{
    public class ProductModel : PageModel
    {
        private readonly ILogger<ProductModel> _logger;
        private readonly ContentService _contentService;

        public ProductModel(ILogger<ProductModel> logger, ContentService contentService)
        {
            _logger = logger;
            _contentService = contentService;
        }

        public ProductPageData PageData { get; set; } = new();

        public async Task OnGetAsync()
        {
            try
            {
                PageData = await _contentService.GetProductPageDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取产品页面数据时发生错误");
                // 使用空数据作为后备
                PageData = new ProductPageData();
            }
        }
    }
}
