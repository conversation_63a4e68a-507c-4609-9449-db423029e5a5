/* 让 .icon-left 内的 .icon 垂直居中 */
.icon-left {
  display: flex;
  align-items: center;
  height: 100%;
  margin-right: 20px;
}

/**
* Professional Business Theme
* Optimized for corporate and professional websites
* Focus on readability, accessibility, and business aesthetics
*/

/*--------------------------------------------------------------
# Font & Color Variables - Professional Theme
--------------------------------------------------------------*/

/* Modern Professional Color System */
:root {
  --background-color: #ffffff;
  --default-color: #475569;
  --heading-color: #1e293b;
  --accent-color: #2f70f5;
  --accent-hover: #1d4ed8;
  --surface-color: #ffffff;
  --contrast-color: #ffffff;
  --border-color: #e2e8f0;
  --muted-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --light-bg: #f8fafc;
  --section-bg: #f1f5f9;
  --primary-blue: #2f70f5;
  --primary-blue-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --primary-lighter: #eff6ff;
}

/* Modern Navigation Colors */
:root {
  --nav-color: #475569;
  --nav-hover-color: #2f70f5;
  --nav-mobile-background-color: rgba(255, 255, 255, 0.98);
  --nav-dropdown-background-color: #ffffff;
  --nav-dropdown-color: #475569;
  --nav-dropdown-hover-color: #2f70f5;
}

/* Professional Color Presets */
.light-background {
  --background-color: var(--light-bg);
  --surface-color: #ffffff;
  --border-color: #e2e8f0;
}

.dark-background {
  --background-color: #1a202c;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: rgba(255, 255, 255, 0.05);
  --contrast-color: #ffffff;
  --border-color: rgba(255, 255, 255, 0.1);
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 16px;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent-hover);
  text-decoration: none;
}

/* Professional Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}
h2 {
  font-size: 1.875rem;
  font-weight: 600;
}
h3 {
  font-size: 1.5rem;
}
h4 {
  font-size: 1.25rem;
}
h5 {
  font-size: 1.125rem;
}
h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1.5rem;
  color: var(--default-color);
  line-height: 1.65;
}

.text-muted {
  color: var(--muted-color) !important;
}

/* PHP Email Form Messages */
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Pulsating Play Button */
.pulsating-play-btn {
  width: 94px;
  height: 94px;
  background: radial-gradient(
    var(--accent-color) 50%,
    color-mix(in srgb, var(--accent-color), transparent 75%) 52%
  );
  border-radius: 50%;
  display: block;
  position: relative;
  overflow: hidden;
}

.pulsating-play-btn:before {
  content: "";
  position: absolute;
  width: 120px;
  height: 120px;
  animation-delay: 0s;
  animation: pulsate-play-btn 2s;
  animation-direction: forwards;
  animation-iteration-count: infinite;
  animation-timing-function: steps;
  opacity: 1;
  border-radius: 50%;
  border: 5px solid color-mix(in srgb, var(--accent-color), transparent 30%);
  top: -15%;
  left: -15%;
  background: rgba(198, 16, 0, 0);
}

.pulsating-play-btn:after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-40%) translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #fff;
  z-index: 100;
  transition: all 400ms cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.pulsating-play-btn:hover:before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-40%) translateY(-50%);
  width: 0;
  height: 0;
  border: none;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #fff;
  z-index: 200;
  animation: none;
  border-radius: 0;
}

.pulsating-play-btn:hover:after {
  border-left: 15px solid var(--accent-color);
  transform: scale(20);
}

@keyframes pulsate-play-btn {
  0% {
    transform: scale(0.6, 0.6);
    opacity: 1;
  }

  100% {
    transform: scale(1, 1);
    opacity: 0;
  }
}

/*--------------------------------------------------------------
# Professional Header
--------------------------------------------------------------*/
.header {
  --background-color: rgba(255, 255, 255, 0.98);
  --default-color: var(--nav-color);
  --heading-color: var(--heading-color);
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 16px 0;
  transition: all 0.2s ease;
  position: relative;
  z-index: 997;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header .logo {
  line-height: 1;
  display: flex;
  align-items: center;
}

.header .logo svg {
  height: 32px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.header .logo svg g {
  fill: var(--accent-color);
}

.header .logo h1 {
  font-size: 28px;
  margin: 0;
  font-weight: 600;
  color: var(--heading-color);
}

.header .header-social-links {
  padding-right: 0;
}

.header .header-social-links .btn-login {
  display: inline-block;
  padding: 10px 20px;
  background: var(--accent-color);
  color: #ffffff;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(43, 108, 176, 0.2);
}

.header .header-social-links .btn-login:hover {
  background: var(--accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(43, 108, 176, 0.3);
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header {
    padding: 12px 0;
  }

  .header .container {
    padding: 0 20px;
  }

  .header .logo {
    order: 1;
    flex: 1;
  }

  .header .logo svg {
    height: 28px;
  }

  .header .header-social-links {
    order: 2;
    margin-right: 15px;
  }

  .header .navmenu {
    order: 3;
  }

  .header .header-social-links .btn-login {
    padding: 8px 20px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 10px 0;
  }

  .header .container {
    padding: 0 15px;
  }

  .header .logo svg {
    height: 26px;
  }

  .header .header-social-links .btn-login {
    padding: 6px 16px;
    font-size: 12px;
  }
}

.scrolled .header {
  --background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
}

/* Professional dark theme for hero section */
.dark-background .header {
  --background-color: rgba(26, 32, 44, 0.95);
  --default-color: #ffffff;
  --heading-color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-background .header .logo svg g {
  fill: #ffffff;
}

.dark-background .header .header-social-links .btn-login {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: none;
}

.dark-background .header .header-social-links .btn-login:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Navmenu - Desktop */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
    gap: 5px;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    padding: 12px 16px;
    font-size: 15px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    transition: all 0.2s ease;
    border-radius: 6px;
    position: relative;
  }

  .navmenu a::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: all 0.2s ease;
    transform: translateX(-50%);
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu li:last-child a {
    padding-right: 18px;
  }

  .navmenu li:hover > a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
    background: rgba(43, 108, 176, 0.08);
  }

  .navmenu li:hover > a::before,
  .navmenu .active::before,
  .navmenu .active:focus::before {
    width: 60%;
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover > a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover > ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover > ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Navmenu - Mobile */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 24px;
    line-height: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 10px;
    border-radius: 8px;
    background: rgba(43, 108, 176, 0.08);
    border: 1px solid rgba(43, 108, 176, 0.15);
    box-shadow: 0 2px 4px rgba(43, 108, 176, 0.1);
    width: 44px;
    height: 44px;
    position: relative;
    overflow: hidden;
    gap: 3px;
  }

  .mobile-nav-toggle span {
    display: block;
    width: 20px;
    height: 2px;
    background: var(--nav-color);
    border-radius: 1px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-nav-toggle::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s;
  }

  .mobile-nav-toggle:hover {
    background: rgba(43, 108, 176, 0.12);
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(43, 108, 176, 0.15);
    border-color: rgba(43, 108, 176, 0.25);
  }

  .mobile-nav-toggle:hover::before {
    left: 100%;
  }

  .mobile-nav-toggle:active {
    transform: scale(0.95);
  }

  .navmenu {
    padding: 0;
    z-index: 9999999 !important;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 80px 20px 30px 20px;
    padding: 24px 0;
    margin: 0;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.98);
    overflow-y: auto;
    transition: all 0.3s ease;
    z-index: 9999999 !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--border-color);
    transform: translateY(-5px);
    opacity: 0;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    white-space: nowrap;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 16px;
    position: relative;
    text-decoration: none;
    min-height: 48px;
    background: transparent;
    overflow: hidden;
  }

  .navmenu a::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-50%);
    border-radius: 3px;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
  }

  .navmenu a::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.05),
      rgba(96, 165, 250, 0.05)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    background-color: rgba(0, 119, 255, 0.1);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: scale(1.1);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
    background: rgba(43, 108, 176, 0.08);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(43, 108, 176, 0.1);
  }

  .navmenu a:hover::before,
  .navmenu .active::before,
  .navmenu .active:focus::before {
    width: 6px;
  }

  .navmenu a:hover::after,
  .navmenu .active::after,
  .navmenu .active:focus::after {
    opacity: 1;
  }

  .navmenu a:active {
    transform: translateX(4px) scale(0.98);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown > .dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: fixed !important;
    font-size: 28px;
    top: 20px !important;
    right: 20px !important;
    margin-right: 0;
    z-index: 99999999 !important;
    background: rgba(239, 68, 68, 0.9);
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
  }

  .mobile-nav-active .mobile-nav-toggle span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
    background: #fff;
  }

  .mobile-nav-active .mobile-nav-toggle span:nth-child(2) {
    opacity: 0;
    background: #fff;
  }

  .mobile-nav-active .mobile-nav-toggle span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
    background: #fff;
  }

  .mobile-nav-active .mobile-nav-toggle:hover {
    background: rgba(239, 68, 68, 1);
    transform: rotate(90deg) scale(1.1);
  }

  .mobile-nav-active .navmenu {
    position: fixed !important;
    overflow: hidden;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: linear-gradient(
      135deg,
      rgba(15, 23, 42, 0.95),
      rgba(30, 41, 59, 0.9)
    );
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999999 !important;
    animation: fadeInOverlay 0.4s ease-out;
  }

  @keyframes fadeInOverlay {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(10px);
    }
  }

  .mobile-nav-active .navmenu > ul {
    display: block;
    transform: translateY(0);
    opacity: 1;
    animation: slideInMenu 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
  }

  @keyframes slideInMenu {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* 移动端触摸优化 */
  @media (max-width: 768px) {
    .mobile-nav-toggle {
      width: 52px;
      height: 52px;
      font-size: 28px;
      -webkit-tap-highlight-color: transparent;
      touch-action: manipulation;
    }

    .navmenu a {
      padding: 20px 30px;
      font-size: 18px;
      min-height: 60px;
      -webkit-tap-highlight-color: transparent;
      touch-action: manipulation;
    }

    .navmenu ul {
      inset: 90px 15px 40px 15px;
      padding: 25px 0;
    }

    /* 添加菜单项之间的分隔线 */
    .navmenu a:not(:last-child)::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 30px;
      right: 30px;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.1),
        transparent
      );
    }

    /* 为菜单项添加涟漪效果 */
    .navmenu a {
      position: relative;
      overflow: hidden;
    }

    .navmenu a:active::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(59, 130, 246, 0.2);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: ripple 0.6s ease-out;
    }

    @keyframes ripple {
      to {
        width: 300px;
        height: 300px;
        opacity: 0;
      }
    }
  }
}

/*--------------------------------------------------------------
# Professional Footer
--------------------------------------------------------------*/
.footer {
  background: #1a202c;
  color: #e2e8f0;
  font-size: 14px;
  position: relative;
  padding: 40px 0 20px 0;
  border-top: 1px solid var(--border-color);
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footer-dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23footer-dots)"/></svg>');
  opacity: 0.6;
}

.footer .container {
  position: relative;
  z-index: 1;
}

.footer .copyright {
  padding: 25px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer .copyright p {
  margin-bottom: 0;
  color: #e2e8f0;
  font-weight: 400;
}

.footer .copyright .sitename {
  color: #ffffff;
  font-weight: 600;
}

.footer .credits {
  margin-top: 8px;
  font-size: 13px;
}

.footer .credits a {
  color: #94a3b8;
  text-decoration: none;
  transition: color 0.3s ease;
  border-bottom: 1px solid transparent;
}

.footer .credits a:hover {
  color: #ffffff;
  border-bottom-color: #ffffff;
}

/* Footer responsive styles */
@media (max-width: 768px) {
  .footer {
    padding: 30px 0 15px 0;
    text-align: center;
  }

  .footer .copyright {
    padding: 20px 0;
  }

  .footer .copyright p {
    font-size: 13px;
    line-height: 1.6;
  }

  .footer .credits {
    font-size: 12px;
    margin-top: 6px;
  }
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 999999;
  overflow: hidden;
  background: var(--background-color);
  transition: all 0.6s ease-out;
}

#preloader:before {
  content: "";
  position: fixed;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
  border: 6px solid #ffffff;
  border-color: var(--accent-color) transparent var(--accent-color) transparent;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: animate-preloader 1.5s linear infinite;
}

@keyframes animate-preloader {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: 15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 40px;
  height: 40px;
  border-radius: 4px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 80px 0;
  scroll-margin-top: 100px;
  overflow: clip;
  position: relative;
}

@media (max-width: 1199px) {
  section,
  .section {
    scroll-margin-top: 66px;
    padding: 60px 0;
  }
}

@media (max-width: 768px) {
  section,
  .section {
    padding: 40px 0;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.section-title h2 {
  color: var(--heading-color);
  font-size: 2.25rem;
  font-weight: 600;
  position: relative;
  margin-bottom: 20px;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.section-title h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: var(--accent-color);
  border-radius: 2px;
}

.section-title p {
  margin-bottom: 0;
  font-size: 1.125rem;
  color: var(--muted-color);
  line-height: 1.6;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .section-title {
    padding-bottom: 40px;
  }

  .section-title h2 {
    font-size: 2rem;
  }

  .section-title p {
    font-size: 1rem;
  }
}
