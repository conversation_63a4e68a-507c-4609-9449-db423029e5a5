﻿/**
 * Article Detail Page JavaScript
 * 处理文章详情页面的交互功能
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeArticleDetail();
});

function initializeArticleDetail() {
    // 生成目录
    generateTableOfContents();
    
    // 初始化目录导航
    initializeTocNavigation();
    
    // 初始化分享功能
    initializeShareButtons();
    
    // 初始化阅读进度
    initializeReadingProgress();
    
    // 初始化图片点击放大
    initializeImageZoom();
    
    // 初始化代码复制功能
    initializeCodeCopy();
}

/**
 * 生成文章目录
 */
function generateTableOfContents() {
    const content = document.querySelector('.article-content');
    const tocContainer = document.getElementById('tableOfContents');
    const tocCard = document.getElementById('tocCard');
    
    if (!content || !tocContainer) return;
    
    const headings = content.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    if (headings.length === 0) {
        return; // 没有标题，不显示目录
    }
    
    // 显示目录卡片
    if (tocCard) {
        tocCard.style.display = 'block';
    }
    
    const tocList = document.createElement('ul');
    tocList.className = 'toc-list';
    
    headings.forEach((heading, index) => {
        // 为标题添加ID
        const id = `heading-${index}`;
        heading.id = id;
        
        // 创建目录项
        const listItem = document.createElement('li');
        const link = document.createElement('a');
        link.href = `#${id}`;
        link.textContent = heading.textContent;
        link.className = `toc-level-${heading.tagName.toLowerCase()}`;
        
        listItem.appendChild(link);
        tocList.appendChild(listItem);
    });
    
    tocContainer.appendChild(tocList);
}

/**
 * 初始化目录导航
 */
function initializeTocNavigation() {
    const tocLinks = document.querySelectorAll('#tableOfContents a');
    const headings = document.querySelectorAll('.article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6');
    
    if (tocLinks.length === 0 || headings.length === 0) return;
    
    // 平滑滚动到目标位置
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 100; // 考虑固定头部的高度
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // 更新活动状态
                updateActiveTocItem(this);
            }
        });
    });
    
    // 滚动时更新活动的目录项
    let ticking = false;
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(function() {
                updateActiveTocOnScroll(headings, tocLinks);
                ticking = false;
            });
            ticking = true;
        }
    });
}

/**
 * 更新活动的目录项
 */
function updateActiveTocItem(activeLink) {
    const tocLinks = document.querySelectorAll('#tableOfContents a');
    tocLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
}

/**
 * 滚动时更新活动的目录项
 */
function updateActiveTocOnScroll(headings, tocLinks) {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    
    let activeIndex = -1;
    
    headings.forEach((heading, index) => {
        const rect = heading.getBoundingClientRect();
        if (rect.top <= 150) { // 考虑固定头部的高度
            activeIndex = index;
        }
    });
    
    // 更新活动状态
    tocLinks.forEach((link, index) => {
        link.classList.toggle('active', index === activeIndex);
    });
}

/**
 * 初始化分享功能
 */
function initializeShareButtons() {
    // 微信分享（实际项目中需要接入微信SDK）
    window.shareToWeChat = function() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                url: window.location.href
            });
        } else {
            // 复制链接到剪贴板
            copyToClipboard(window.location.href);
            showToast('链接已复制到剪贴板');
        }
    };
    
    // 微博分享
    window.shareToWeibo = function() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        const shareUrl = `https://service.weibo.com/share/share.php?url=${url}&title=${title}`;
        window.open(shareUrl, '_blank', 'width=600,height=400');
    };
    
    // 复制链接
    window.copyLink = function() {
        copyToClipboard(window.location.href);
        showToast('链接已复制到剪贴板');
    };
}

/**
 * 初始化阅读进度
 */
function initializeReadingProgress() {
    const article = document.querySelector('.article-content');
    if (!article) return;
    
    // 创建进度条
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: var(--accent-color);
        z-index: 9999;
        transition: width 0.3s ease;
    `;
    document.body.appendChild(progressBar);
    
    // 更新进度
    function updateProgress() {
        const articleRect = article.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const articleHeight = article.offsetHeight;
        const scrolled = Math.max(0, -articleRect.top);
        const progress = Math.min(100, (scrolled / (articleHeight - windowHeight)) * 100);
        
        progressBar.style.width = progress + '%';
    }
    
    window.addEventListener('scroll', throttle(updateProgress, 10));
}

/**
 * 初始化图片点击放大
 */
function initializeImageZoom() {
    const images = document.querySelectorAll('.article-content img');
    
    images.forEach(img => {
        img.style.cursor = 'zoom-in';
        img.addEventListener('click', function() {
            showImageModal(this.src, this.alt);
        });
    });
}

/**
 * 显示图片模态框
 */
function showImageModal(src, alt) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        cursor: zoom-out;
    `;
    
    const img = document.createElement('img');
    img.src = src;
    img.alt = alt;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    `;
    
    modal.appendChild(img);
    document.body.appendChild(modal);
    
    // 点击关闭
    modal.addEventListener('click', function() {
        document.body.removeChild(modal);
    });
    
    // ESC键关闭
    const handleKeydown = function(e) {
        if (e.key === 'Escape') {
            document.body.removeChild(modal);
            document.removeEventListener('keydown', handleKeydown);
        }
    };
    document.addEventListener('keydown', handleKeydown);
}

/**
 * 初始化代码复制功能
 */
function initializeCodeCopy() {
    const codeBlocks = document.querySelectorAll('.article-content pre');
    
    codeBlocks.forEach(block => {
        const button = document.createElement('button');
        button.className = 'btn btn-sm btn-outline-secondary copy-code-btn';
        button.innerHTML = '<i class="bi bi-clipboard"></i>';
        button.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        `;
        
        block.style.position = 'relative';
        block.appendChild(button);
        
        button.addEventListener('click', function() {
            const code = block.querySelector('code');
            if (code) {
                copyToClipboard(code.textContent);
                showToast('代码已复制到剪贴板');
                
                // 临时改变按钮状态
                button.innerHTML = '<i class="bi bi-check"></i>';
                setTimeout(() => {
                    button.innerHTML = '<i class="bi bi-clipboard"></i>';
                }, 2000);
            }
        });
    });
}

/**
 * 复制到剪贴板
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text);
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
}

/**
 * 显示提示消息
 */
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast-message';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--success-color);
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
