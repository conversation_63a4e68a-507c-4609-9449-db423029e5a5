﻿using System.Text.Json.Serialization;

namespace FaoBao.HomePage.Dtos.YuQue;

public class DocDetailDto:BaseDocDto
{
    [JsonPropertyName("format")]
    public string Format { get; set; } = string.Empty;

    [JsonPropertyName("body_draft")]
    public string BodyDraft { get; set; } = string.Empty;

    [JsonPropertyName("body")]
    public string Body { get; set; } = string.Empty;

    [JsonPropertyName("body_html")]
    public string BodyHtml { get; set; } = string.Empty;

    [JsonPropertyName("body_lake")]
    public string BodyLake { get; set; } = string.Empty;

    [JsonPropertyName("book")]
    public BookDto? Book { get; set; }

    [Json<PERSON>ropertyName("user")]
    public UserDto? User { get; set; }

    [JsonPropertyName("tags")]
    public List<object> Tags { get; set; } = new();

    [JsonPropertyName("latest_version_id")]
    public long LatestVersionId { get; set; }

    [JsonPropertyName("creator")]
    public UserDto? Creator { get; set; } 
}