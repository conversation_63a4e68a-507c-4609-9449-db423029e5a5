﻿using System.Text.Json.Serialization;

namespace FaoBao.HomePage.Dtos.YuQue;

public class TocItemDto
{
    [JsonPropertyName("uuid")]
    public string Uuid { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    [JsonPropertyName("slug")]
    public string Slug { get; set; } = string.Empty;

    [JsonPropertyName("id")]
    public long? Id { get; set; }

    [JsonPropertyName("doc_id")]
    public long? DocId { get; set; } 

    [JsonPropertyName("level")]
    public int Level { get; set; }

    [JsonPropertyName("depth")]
    public int Depth { get; set; }

    [JsonPropertyName("open_window")]
    public int OpenWindow { get; set; }

    [JsonPropertyName("visible")]
    public int Visible { get; set; }

    [JsonPropertyName("prev_uuid")]
    public string PrevUuid { get; set; } = string.Empty;

    [JsonPropertyName("sibling_uuid")]
    public string SiblingUuid { get; set; } = string.Empty;

    [JsonPropertyName("child_uuid")]
    public string ChildUuid { get; set; } = string.Empty;

    [JsonPropertyName("parent_uuid")]
    public string ParentUuid { get; set; } = string.Empty;

    [JsonPropertyName("_serializer")]
    public string Serializer { get; set; } = string.Empty;
}