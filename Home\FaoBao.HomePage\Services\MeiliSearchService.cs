using Meilisearch;
using System.Text.Json.Serialization;

namespace FaoBao.HomePage.Services;

/// <summary>
/// MeiliSearch 全文搜索服务 - 集成Jieba中文分词
/// </summary>
public class MeiliSearchService
{
    private readonly MeilisearchClient _client;
    private readonly string _indexName;
    private readonly ILogger<MeiliSearchService> _logger;
    private readonly JiebaService _jiebaService;
  
    public MeiliSearchService(IConfiguration configuration, ILogger<MeiliSearchService> logger, JiebaService jiebaService)
    {
        _logger = logger;
        _jiebaService = jiebaService;
        var meiliConfig = configuration.GetSection("MeiliSearch");
        var host = meiliConfig["Host"];
        var apiKey = meiliConfig["ApiKey"];
        _indexName = meiliConfig["IndexName"] ?? "yuque_documents";
        _client = new MeilisearchClient(host, apiKey);
    }

    /// <summary>
    /// 初始化索引
    /// </summary>
    public async Task InitializeIndexAsync()
    {
        try
        {
            var index = await _client.GetIndexAsync(_indexName);
        //    await _client.DeleteIndexAsync(_indexName);
            _logger.LogInformation("MeiliSearch 索引已存在: {IndexName}", _indexName);
        }
        catch (Exception)
        {
            // 索引不存在，创建新索引
            try
            {
                await _client.CreateIndexAsync(_indexName, "id");
                _logger.LogInformation("成功创建 MeiliSearch 索引: {IndexName}", _indexName);
                
                var index = await _client.GetIndexAsync(_indexName);
                await index.UpdateSearchableAttributesAsync(["title", "content"]);
                await index.UpdateFilterableAttributesAsync(["type", "bookSlug"]);
                await index.UpdateSortableAttributesAsync(["updatedAt", "createdAt"]);

                _logger.LogInformation("成功配置 MeiliSearch 索引属性");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建 MeiliSearch 索引时发生错误");
                throw;
            }
        }
    }

    /// <summary>
    /// 添加或更新文档到搜索索引
    /// </summary>
    /// <param name="document">要索引的文档</param>
    public async Task<bool> IndexDocumentAsync(SearchableDocument document)
    {
        try
        {
            // 使用Jieba对内容进行分词，生成content_tokens字段
            document.Content = _jiebaService.GenerateSearchKeywords(document.Title, document.Content);

            var index = await _client.GetIndexAsync(_indexName);
            await index.AddDocumentsAsync([document]);
            _logger.LogDebug("成功索引文档: {DocumentId}，分词tokens: {TokensCount} 个词",
                document.Id, document.Content?.Split(' ').Length ?? 0);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "索引文档时发生错误: {DocumentId}", document.Id);
            return false;
        }
    }

    /// <summary>
    /// 批量添加或更新文档到搜索索引
    /// </summary>
    /// <param name="documents">要索引的文档列表</param>
    public async Task<bool> IndexDocumentsAsync(IEnumerable<SearchableDocument> documents)
    {
        try
        {
            // 为每个文档生成分词tokens
            var documentsWithTokens = documents.ToList();
            foreach (var document in documentsWithTokens)
            {
                document.Content = _jiebaService.GenerateSearchKeywords(document.Title, document.Content);
            }

            var index = await _client.GetIndexAsync(_indexName);
            await index.AddDocumentsAsync(documentsWithTokens);

            _logger.LogDebug("成功批量索引 {Count} 个文档，已生成中文分词tokens", documentsWithTokens.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量索引文档时发生错误");
            return false;
        }
    }

    /// <summary>
    /// 搜索文档
    /// </summary>
    /// <param name="query">搜索查询</param>
    /// <param name="filter">过滤条件</param>
    /// <param name="limit">返回结果数量限制</param>
    /// <param name="offset">偏移量</param>
    /// <returns>搜索结果</returns>
    public async Task<SearchResult<SearchableDocument>> SearchAsync(
        string query,
        string? filter = null,
        int limit = 20,
        int offset = 0)
    {
        try
        {
            // 使用Jieba对搜索查询进行分词优化
            var optimizedQuery = _jiebaService.OptimizeSearchQuery(query);
            _logger.LogDebug("搜索查询优化: '{OriginalQuery}' -> '{OptimizedQuery}'", query, optimizedQuery);

            var index = await _client.GetIndexAsync(_indexName);
            var result = await index.SearchAsync<SearchableDocument>(optimizedQuery, new SearchQuery
            {
                AttributesToHighlight = ["title", "content"],
                HighlightPreTag = "<mark>",
                HighlightPostTag = "</mark>",
                Filter = filter,
                Limit = limit,
                Offset = offset
            });

            // 处理高亮内容，优先显示高亮
            if (result.Hits != null)
            {
                foreach (var doc in result.Hits)
                {
                    if (doc.Formatted != null)
                    {
                        if (!string.IsNullOrEmpty(doc.Formatted.Title))
                            doc.Title = doc.Formatted.Title;
                        if (!string.IsNullOrEmpty(doc.Formatted.Content))
                            doc.Content = doc.Formatted.Content;
                    }
                }
            }

            return new SearchResult<SearchableDocument>
            {
                Hits = result.Hits ?? new List<SearchableDocument>(),
                Query = query, // 保持原始查询用于显示
                ProcessingTimeMs = result.ProcessingTimeMs,
                Limit = limit,
                Offset = offset,
                EstimatedTotalHits = result.Hits?.Count ?? 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索时发生错误: {Query}", query);
            return new SearchResult<SearchableDocument>
            {
                Hits = new List<SearchableDocument>(),
                Query = query,
                ProcessingTimeMs = 0,
                Limit = limit,
                Offset = offset,
                EstimatedTotalHits = 0
            };
        }
    }

    /// <summary>
    /// 删除文档
    /// </summary>
    /// <param name="documentId">文档ID</param>
    public async Task<bool> DeleteDocumentAsync(string documentId)
    {
        try
        {
            var index = await _client.GetIndexAsync(_indexName);
            await index.DeleteOneDocumentAsync(documentId);

            _logger.LogDebug("成功删除文档: {DocumentId}", documentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文档时发生错误: {DocumentId}", documentId);
            return false;
        }
    }

    /// <summary>
    /// 清空索引
    /// </summary>
    public async Task<bool> ClearIndexAsync()
    {
        try
        {
            var index = await _client.GetIndexAsync(_indexName);
            await index.DeleteAllDocumentsAsync();

            _logger.LogInformation("成功清空索引: {IndexName}", _indexName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空索引时发生错误: {IndexName}", _indexName);
            return false;
        }
    }
}

/// <summary>
/// 可搜索的文档模型
/// </summary>
public class SearchableDocument
{
    [JsonPropertyName("id")] 
    public required string Id { get; set; }

    [JsonPropertyName("title")] 
    public required string Title { get; set; }
    

    // 高亮后的 title
    [JsonPropertyName("_formatted")]
    public FormattedDocument? Formatted { get; set; }

    [JsonPropertyName("type")] 
    public required string Type { get; set; } // "doc", "repo", "toc"

    [JsonPropertyName("bookSlug")] 
    public string? BookSlug { get; set; }

    [JsonPropertyName("url")] 
    public required string Url { get; set; }

    [JsonPropertyName("createdAt")] 
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 中文分词后的内容，用于提升中文搜索准确率
    /// </summary>
    [JsonPropertyName("content")]
    public string? Content { get; set; }

}

public class FormattedDocument
{
    [JsonPropertyName("title")]
    public string? Title { get; set; }
    [JsonPropertyName("content")]
    public string? Content { get; set; }
}

/// <summary>
/// 搜索结果模型
/// </summary>
/// <typeparam name="T">文档类型</typeparam>
public class SearchResult<T>
{
    public IEnumerable<T> Hits { get; set; } = new List<T>();
    public string Query { get; set; } = string.Empty;
    public int ProcessingTimeMs { get; set; }
    public int Limit { get; set; }
    public int Offset { get; set; }
    public int EstimatedTotalHits { get; set; }
}