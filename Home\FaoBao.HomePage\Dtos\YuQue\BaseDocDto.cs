﻿using System.Text.Json.Serialization;

namespace FaoBao.HomePage.Dtos.YuQue;

public class BaseDocDto
{
        [JsonPropertyName("id")]
        public long Id { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("slug")]
        public string Slug { get; set; } = string.Empty;

        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("cover")]
        public string Cover { get; set; } = string.Empty;

        [JsonPropertyName("user_id")]
        public long UserId { get; set; }
        
        [JsonPropertyName("book_id")]
        public long BookId { get; set; }

        [JsonPropertyName("last_editor_id")]
        public long LastEditorId { get; set; }

        [JsonPropertyName("public")]
        public int Public { get; set; }

        [JsonPropertyName("status")]
        public int Status { get; set; }

        [JsonPropertyName("likes_count")]
        public int LikesCount { get; set; }

        [JsonPropertyName("read_count")]
        public int ReadCount { get; set; }

        [JsonPropertyName("comments_count")]
        public int CommentsCount { get; set; }

        [JsonPropertyName("word_count")]
        public int WordCount { get; set; }

        [JsonPropertyName("created_at")]
        public DateTime? CreatedAt { get; set; } 

        [JsonPropertyName("updated_at")]
        public DateTime? UpdatedAt { get; set; } 

        [JsonPropertyName("content_updated_at")]
        public DateTime? ContentUpdatedAt { get; set; } 

        [JsonPropertyName("published_at")]
        public DateTime? PublishedAt { get; set; }

        [JsonPropertyName("first_published_at")]
        public DateTime? FirstPublishedAt { get; set; } 

        [JsonPropertyName("hits")]
        public int Hits { get; set; }

        [JsonPropertyName("_serializer")]
        public string Serializer { get; set; } = string.Empty;
}