﻿/*--------------------------------------------------------------
# Articles Page Styles - Bootstrap v5 Compatible
--------------------------------------------------------------*/

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.search-container .form-control {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-container .btn {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Sidebar */
.sidebar-sticky {
    position: sticky;
    top: 100px;
    z-index: 1020;
}

.list-group-item.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* Article Cards */
.article-card {
    transition: all 0.3s ease;
    overflow: hidden;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15) !important;
}

.article-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.article-tags .badge {
    font-size: 0.75rem;
    font-weight: normal;
}

.author-info img {
    object-fit: cover;
}

/* Article Detail */
.article-detail {
    max-width: none;
}

.article-title {
    line-height: 1.2;
    color: var(--heading-color);
}

.article-summary {
    font-size: 1.125rem;
    line-height: 1.6;
}

.article-meta {
    font-size: 0.9rem;
}

.article-featured-image {
    max-height: 400px;
    overflow: hidden;
}

.article-featured-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

/* Article Content Styling */
.article-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--default-color);
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
    color: var(--heading-color);
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.article-content h1 {
    font-size: 2rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.article-content h2 {
    font-size: 1.75rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.3rem;
}

.article-content h3 {
    font-size: 1.5rem;
}

.article-content h4 {
    font-size: 1.25rem;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content ul,
.article-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-content li {
    margin-bottom: 0.5rem;
}

.article-content blockquote {
    border-left: 4px solid var(--accent-color);
    background: var(--light-background, #f8fafc);
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    font-style: italic;
}

.article-content code {
    background: var(--light-background, #f8fafc);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9em;
    color: var(--accent-color);
}

.article-content pre {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.article-content pre code {
    background: none;
    padding: 0;
    color: inherit;
}

.article-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 1.5rem 0;
}

.article-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    border: 1px solid var(--border-color);
}

.article-content th,
.article-content td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.article-content th {
    background: var(--light-background, #f8fafc);
    font-weight: 600;
}

/* Table of Contents */
.toc-nav {
    max-height: 400px;
    overflow-y: auto;
}

.toc-nav ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.toc-nav ul ul {
    padding-left: 1rem;
    margin-top: 0.5rem;
}

.toc-nav a {
    display: block;
    padding: 0.5rem 0;
    color: var(--default-color);
    text-decoration: none;
    border-left: 2px solid transparent;
    padding-left: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    line-height: 1.4;
}

.toc-nav a:hover,
.toc-nav a.active {
    color: var(--accent-color);
    border-left-color: var(--accent-color);
    background: rgba(59, 130, 246, 0.05);
}

/* Share Buttons */
.share-buttons .btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Featured Articles in Sidebar */
.featured-article-item h6 a {
    color: var(--heading-color);
    line-height: 1.4;
}

.featured-article-item h6 a:hover {
    color: var(--accent-color);
}

.related-article-item h6 a {
    color: var(--heading-color);
    line-height: 1.4;
}

.related-article-item h6 a:hover {
    color: var(--accent-color);
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .sidebar-sticky {
        position: static;
        top: auto;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 1rem !important;
        align-items: flex-start !important;
    }
    
    .article-stats {
        flex-direction: column;
        gap: 0.5rem !important;
    }
}

@media (max-width: 767.98px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .article-title {
        font-size: 2rem !important;
    }
    
    .article-summary {
        font-size: 1rem;
    }
    
    .article-content {
        font-size: 1rem;
    }
    
    .article-content h1 {
        font-size: 1.75rem;
    }
    
    .article-content h2 {
        font-size: 1.5rem;
    }
    
    .article-content h3 {
        font-size: 1.25rem;
    }
}

/* Animation Enhancements */
.article-card,
.card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pagination Styling */
.pagination .page-link {
    color: var(--default-color);
    border-color: var(--border-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.pagination .page-link:hover {
    color: var(--accent-hover);
    border-color: var(--accent-color);
}

/* Badge Styling */
.badge {
    font-weight: 500;
}

/* Alert Styling */
.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
    color: var(--accent-color);
}
