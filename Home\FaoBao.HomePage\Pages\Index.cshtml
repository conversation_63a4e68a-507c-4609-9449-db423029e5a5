﻿@page
@model IndexModel
@{
    ViewData["Title"] = "法宝 - 共建数字化产业生态";
}
@section Styles {
    <link href="/css/index.css" rel="stylesheet"  asp-append-version="true" />
}
@section Scripts {
    <script src="/js/index.js" asp-append-version="true"></script>
}


<section id="hero" class="hero section">
    <div id="heroCarousel" class="carousel slide hero-carousel" data-bs-ride="carousel" data-bs-interval="5000">
        <!-- 轮播指示器 -->
        <div class="carousel-indicators">
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"></button>
        </div>

        <!-- 轮播内容 -->
        <div class="carousel-inner">
            <!-- 第一张幻灯片 - 企业数字化转型 -->
            <div class="carousel-item active">
                <div class="hero-slide hero-slide-1">
                    <div class="container">
                        <div class="row align-items-center min-vh-100">
                            <div class="col-lg-6">
                                <div class="hero-content">
                                    <div class="hero-badge">企业级解决方案</div>
                                    <h1 class="hero-title">数字化转型<br>从这里开始</h1>
                                    <p class="hero-subtitle">为企业提供全方位数字化解决方案，助力业务增长与创新发展，让技术成为您的核心竞争力</p>
                                    <div class="hero-buttons">
                                        <a href="#" class="btn btn-primary btn-lg">免费咨询</a>
                                        <a href="#" class="btn btn-outline-dark btn-lg">解决方案</a>
                                    </div>
                                    <div class="hero-stats-inline">
                                        <div class="stat-item">
                                            <span class="stat-number">500+</span>
                                            <span class="stat-label">企业客户</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number">99.9%</span>
                                            <span class="stat-label">服务可用性</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="hero-image">
                                    <div class="hero-visual">
                                        <div class="business-card">
                                            <div class="card-header">
                                                <div class="card-title">业务增长</div>
                                                <div class="card-trend">↗ +24%</div>
                                            </div>
                                            <div class="card-chart"></div>
                                        </div>
                                        <div class="business-card card-2">
                                            <div class="card-header">
                                                <div class="card-title">运营效率</div>
                                                <div class="card-trend">↗ +18%</div>
                                            </div>
                                            <div class="card-chart"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二张幻灯片 - 技术服务 -->
            <div class="carousel-item">
                <div class="hero-slide hero-slide-2">
                    <div class="container">
                        <div class="row align-items-center min-vh-100">
                            <div class="col-lg-6">
                                <div class="hero-content">
                                    <div class="hero-badge">专业技术服务</div>
                                    <h1 class="hero-title">云端基础设施<br>安全可靠</h1>
                                    <p class="hero-subtitle">基于云原生架构，提供高可用、高性能的企业级服务，7×24小时专业技术支持</p>
                                    <div class="hero-buttons">
                                        <a href="#" class="btn btn-primary btn-lg">技术支持</a>
                                        <a href="#" class="btn btn-outline-dark btn-lg">架构咨询</a>
                                    </div>
                                    <div class="hero-features">
                                        <div class="feature-item">
                                            <i class="bi bi-shield-check"></i>
                                            <span>企业级安全</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="bi bi-lightning"></i>
                                            <span>高性能计算</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="bi bi-headset"></i>
                                            <span>24/7支持</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="hero-image">
                                    <div class="hero-visual">
                                        <div class="tech-grid">
                                            <div class="tech-item">
                                                <div class="tech-icon">
                                                    <i class="bi bi-cloud"></i>
                                                </div>
                                                <div class="tech-label">云计算</div>
                                            </div>
                                            <div class="tech-item">
                                                <div class="tech-icon">
                                                    <i class="bi bi-database"></i>
                                                </div>
                                                <div class="tech-label">大数据</div>
                                            </div>
                                            <div class="tech-item">
                                                <div class="tech-icon">
                                                    <i class="bi bi-cpu"></i>
                                                </div>
                                                <div class="tech-label">AI智能</div>
                                            </div>
                                            <div class="tech-item">
                                                <div class="tech-icon">
                                                    <i class="bi bi-shield-lock"></i>
                                                </div>
                                                <div class="tech-label">安全防护</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三张幻灯片 - 行业解决方案 -->
            <div class="carousel-item">
                <div class="hero-slide hero-slide-3">
                    <div class="container">
                        <div class="row align-items-center min-vh-100">
                            <div class="col-lg-6">
                                <div class="hero-content">
                                    <div class="hero-badge">行业解决方案</div>
                                    <h1 class="hero-title">深度行业洞察<br>精准解决方案</h1>
                                    <p class="hero-subtitle">基于多年行业经验，为不同行业提供定制化解决方案，助力企业实现数字化转型升级</p>
                                    <div class="hero-buttons">
                                        <a href="#" class="btn btn-primary btn-lg">行业方案</a>
                                        <a href="#" class="btn btn-outline-dark btn-lg">成功案例</a>
                                    </div>
                                    <div class="hero-industries">
                                        <span class="industry-tag">金融</span>
                                        <span class="industry-tag">制造</span>
                                        <span class="industry-tag">零售</span>
                                        <span class="industry-tag">教育</span>
                                        <span class="industry-tag">医疗</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="hero-image">
                                    <div class="hero-visual">
                                        <div class="solution-diagram">
                                            <div class="solution-center">
                                                <div class="center-icon">
                                                    <i class="bi bi-gear"></i>
                                                </div>
                                                <div class="center-label">核心平台</div>
                                            </div>
                                            <div class="solution-nodes">
                                                <div class="node node-1">
                                                    <i class="bi bi-people"></i>
                                                    <span>用户管理</span>
                                                </div>
                                                <div class="node node-2">
                                                    <i class="bi bi-graph-up"></i>
                                                    <span>数据分析</span>
                                                </div>
                                                <div class="node node-3">
                                                    <i class="bi bi-shield"></i>
                                                    <span>安全防护</span>
                                                </div>
                                                <div class="node node-4">
                                                    <i class="bi bi-cloud"></i>
                                                    <span>云端部署</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 轮播控制按钮 -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
        </button>
    </div>
</section>

<section id="advantage" class="advantage section">
    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
        <h2>共建数字化产业生态</h2>
        <p>
            为数字行业赋能，做商家经营“法宝”
        </p>
    </div>
    <!-- End Section Title -->

    <div class="container">
        <div class="row g-4">
            @foreach (var advantage in Model.PageData.Advantages.Where(a => a.IsActive).OrderBy(a => a.Order))
            {
                <div class="col-lg-6" data-aos="zoom-in" data-aos-delay="@((advantage.Order - 1) * 100)">
                    <div class="icon-box position-relative h-100">
                        <div class="d-flex align-items-center">
                            <i class="@advantage.Icon"></i>
                            <h4>
                                <a href="#" class="stretched-link">@advantage.Title</a>
                            </h4>
                        </div>
                        <p>@advantage.Description</p>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<section id="clients" class="clients section">
    <div class="container">
        <div class="swiper init-swiper">
            <script type="application/json" class="swiper-config">
                {
                  "loop": true,
                  "speed": 600,
                  "autoplay": {
                    "delay": 5000
                  },
                  "slidesPerView": "auto",
                  "pagination": {
                    "el": ".swiper-pagination",
                    "type": "bullets",
                    "clickable": true
                  },
                  "breakpoints": {
                    "320": {
                      "slidesPerView": 2,
                      "spaceBetween": 40
                    },
                    "480": {
                      "slidesPerView": 3,
                      "spaceBetween": 60
                    },
                    "640": {
                      "slidesPerView": 4,
                      "spaceBetween": 80
                    },
                    "992": {
                      "slidesPerView": 6,
                      "spaceBetween": 120
                    }
                  }
                }
            </script>
            <div class="swiper-wrapper align-items-center">
                <div class="swiper-slide">
                    <img src="/img/clients/taobao.png"
                         class="img-fluid"
                         alt="" />
                </div>
                <div class="swiper-slide">
                    <img src="/img/clients/tmall.png"
                         class="img-fluid"
                         alt="" />
                </div>
                <div class="swiper-slide">
                    <img src="/img/clients/xianyu.png"
                         class="img-fluid"
                         alt="" />
                </div>
                <div class="swiper-slide">
                    <img src="/img/clients/douyin.png"
                         class="img-fluid"
                         alt="" />
                </div>
                <div class="swiper-slide">
                    <img src="/img/clients/jingdong.png"
                         class="img-fluid"
                         alt="" />
                </div>
                <div class="swiper-slide">
                    <img src="/img/clients/pdd.png"
                         class="img-fluid"
                         alt="" />
                </div>
                <div class="swiper-slide">
                    <img src="/img/clients/xiaohongshu.png"
                         class="img-fluid"
                         alt="" />
                </div>
                <div class="swiper-slide">
                    <img src="/img/clients/weidian.png"
                         class="img-fluid"
                         alt="" />
                </div>
                <div class="swiper-slide">
                    <img src="/img/clients/youzan.png"
                         class="img-fluid"
                         alt="" />
                </div>
            </div>
        </div>
    </div>
</section>

<section id="scene" class="scene section">
    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
        <h2>应用场景</h2>
        <p>覆盖数字产品全品类，提供从订单接收到履约交付的一站式解决方案</p>
    </div>
    <!-- End Section Title -->
    <div class="container">
        <div class="row"
             data-aos="fade-up"
             data-aos-delay="100">
            @foreach (var scene in Model.PageData.Scenes.Where(s => s.IsActive).OrderBy(s => s.Order))
            {
                <div data-aos="fade-up"
                     data-aos-delay="@((scene.Order - 1) * 100 + 200)">
                    <a href="@scene.Url"
                       class="category"
                       data-theme="@scene.ColorTheme">
                        <i class="@scene.Icon"></i>
                        <h3>@scene.Title</h3>
                        <span>@scene.Description</span>
                    </a>
                </div>
            }
        </div>

        <div class="justify-content-center"
             data-aos="fade-up"
             data-aos-delay="1000">
            <div class=" text-center">
                <div class="scene-footer">
                    <p>更多应用场景和成功案例，欢迎联系我们获取专业咨询服务</p>
                    <div class="scene-stats">
                        <div class="stat-item">
                            <span class="stat-number">20+</span>
                            <span class="stat-label">应用场景</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">5000+</span>
                            <span class="stat-label">合作商家</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">99.99%</span>
                            <span class="stat-label">服务可用性</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<section id="introduction" class="services section">
    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
        <h2>核心能力</h2>
        <p>
            基于深度行业洞察，打造专业化功能模块，满足不同经营场景需求，全面提升商家运营效率
        </p>
    </div>
    <!-- End Section Title -->

    <div class="container">
        <div class="row g-4">
            @foreach (var service in Model.PageData.Services.Where(s => s.IsActive).OrderBy(s => s.Order))
            {
                <div class="col-xl-4 col-lg-6 col-md-12 p-3"
                     data-aos="fade-up"
                     data-aos-delay="@(service.Order * 100)">
                    <div class="service-item d-flex">
                        <div class="icon-left">
                            <div class="icon">
                                <i class="@service.Icon"></i>
                            </div>
                        </div>
                        <div>
                            <h4 class="title">@service.Title</h4>
                            <p class="description">@service.Description</p>
                            <a href="@service.ReadMoreUrl" class="readmore">
                                <span>查看详情</span><i class="bi bi-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<section id="faq" class="faq section light-background">
    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
        <h2>常见问题解答</h2>
        <p>
            帮助您快速解答疑惑，如有更多疑问，欢迎联系我们的客服团队，为您专业解答
        </p>
    </div>
    <!-- End Section Title -->

    <div class="container">
        <div class="row">
            @{
                var faqs = Model.PageData.Faqs.Where(f => f.IsActive).OrderBy(f => f.Order).ToList();
                var halfCount = (faqs.Count + 1) / 2;
                var leftFaqs = faqs.Take(halfCount).ToList();
                var rightFaqs = faqs.Skip(halfCount).ToList();
            }

            <div class="col-lg-6" data-aos="fade-up" data-aos-delay="100">
                <div class="faq-container">
                    @for (int i = 0; i < leftFaqs.Count; i++)
                    {
                        var faq = leftFaqs[i];
                        <div class="faq-item @(i == 0 ? "faq-active" : "")">
                            <h3>@faq.Question</h3>
                            <div class="faq-content">
                                <p>@faq.Answer</p>
                            </div>
                            <i class="faq-toggle bi bi-chevron-right"></i>
                        </div>
                    }
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-up" data-aos-delay="200">
                <div class="faq-container">
                    @foreach (var faq in rightFaqs)
                    {
                        <div class="faq-item">
                            <h3>@faq.Question</h3>
                            <div class="faq-content">
                                <p>@faq.Answer</p>
                            </div>
                            <i class="faq-toggle bi bi-chevron-right"></i>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</section>
