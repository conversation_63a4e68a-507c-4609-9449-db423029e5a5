using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using FaoBao.HomePage.Services;
using FaoBao.HomePage.Models;

namespace FaoBao.HomePage.Pages
{
    public class IndexModel : PageModel
    {
        private readonly ILogger<IndexModel> _logger;
        private readonly ContentService _contentService;

        public IndexModel(ILogger<IndexModel> logger, ContentService contentService)
        {
            _logger = logger;
            _contentService = contentService;
        }

        public HomePageData PageData { get; set; } = new();

        public async Task OnGetAsync()
        {
            try
            {
                PageData = await _contentService.GetHomePageDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取首页数据时发生错误");
                // 使用空数据作为后备
                PageData = new HomePageData();
            }
        }
    }
}
