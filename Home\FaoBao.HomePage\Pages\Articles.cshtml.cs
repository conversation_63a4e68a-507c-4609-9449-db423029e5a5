﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using FaoBao.HomePage.Services;
using FaoBao.HomePage.Models;

namespace FaoBao.HomePage.Pages
{
    public class ArticlesModel : PageModel
    {
        private readonly ILogger<ArticlesModel> _logger;
        private readonly ContentService _contentService;

        public ArticlesModel(ILogger<ArticlesModel> logger, ContentService contentService)
        {
            _logger = logger;
            _contentService = contentService;
        }

        public ArticlePageData PageData { get; set; } = new();
        
        [BindProperty(SupportsGet = true)]
        public int CurrentPage { get; set; } = 1;
        
        [BindProperty(SupportsGet = true)]
        public string? Category { get; set; }
        
        [BindProperty(SupportsGet = true)]
        public string? Search { get; set; }

        public async Task OnGetAsync()
        {
            try
            {
                PageData = await _contentService.GetArticlePageDataAsync(CurrentPage, 12, Category, Search);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文章页面数据时发生错误");
                // 使用空数据作为后备
                PageData = new ArticlePageData();
            }
        }

        /// <summary>
        /// 搜索文章
        /// </summary>
        public async Task<IActionResult> OnGetSearchAsync(string keyword, string? categoryId)
        {
            try
            {
                var results = await _contentService.SearchArticlesAsync(keyword, categoryId);
                return new JsonResult(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索文章时发生错误");
                return new JsonResult(new ArticleSearchResult());
            }
        }
    }
}
