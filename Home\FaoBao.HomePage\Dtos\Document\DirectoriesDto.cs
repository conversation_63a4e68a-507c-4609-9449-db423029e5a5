﻿using System.Text.Json.Serialization;

namespace FaoBao.HomePage.Dtos.Document
{
    /// <summary>
    /// 目录显示数据传输对象
    /// </summary>
    public class DirectoriesDto
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        public string Uuid { get; set; } = string.Empty;

        /// <summary>
        /// 类型 (DOC 或 TITLE)
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 链接地址
        /// </summary>
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// 文档标识
        /// </summary>
        public string Slug { get; set; } = string.Empty;

        /// <summary>
        /// 文档ID
        /// </summary>
        public long? DocId { get; set; }

        /// <summary>
        /// 层级深度 (0为顶级)
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 嵌套深度
        /// </summary>
        public int Depth { get; set; }

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool Visible { get; set; } = true;

        /// <summary>
        /// 父级UUID
        /// </summary>
        public string ParentUuid { get; set; } = string.Empty;

        /// <summary>
        /// 子级UUID
        /// </summary>
        public string ChildUuid { get; set; } = string.Empty;

        /// <summary>
        /// 图标类名
        /// </summary>
        public string IconClass { get; set; } = string.Empty;

        /// <summary>
        /// 是否有子项
        /// </summary>
        public bool HasChildren { get; set; }

        /// <summary>
        /// 是否展开
        /// </summary>
        public bool IsExpanded { get; set; } = false;

        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsActive { get; set; } = false;

        /// <summary>
        /// 子项列表
        /// </summary>
        public List<DirectoriesDto> Children { get; set; } = new List<DirectoriesDto>();

        /// <summary>
        /// 显示顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 描述信息
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 阅读次数
        /// </summary>
        public int ReadCount { get; set; }

        /// <summary>
        /// 是否为新文档
        /// </summary>
        public bool IsNew { get; set; }

        /// <summary>
        /// 文档状态
        /// </summary>
        public string Status { get; set; } = "normal";
    }
    
}
