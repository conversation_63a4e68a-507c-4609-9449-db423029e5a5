﻿@page "/news/{slug}"
@model FaoBao.HomePage.Pages.ArticleDetailModel

@section Styles {
    <link href="/css/articles.css" rel="stylesheet" asp-append-version="true"/>
    <link href="//editor.yuque.com/ne-editor/lake-content-v1.css" rel="stylesheet"/>
}

@section Scripts {
    <script src="/js/article-detail.js" asp-append-version="true"></script>
}

@if (Model.Article != null)
{
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="bg-light py-3">
        <div class="container">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a asp-page="/Index" class="text-decoration-none">首页</a>
                </li>
                <li class="breadcrumb-item">
                    <a asp-page="/Articles" class="text-decoration-none">技术文章</a>
                </li>
                <li class="breadcrumb-item">
                    <a asp-page="/Articles" 
                       asp-route-category="@Model.Article.Category.Id" 
                       class="text-decoration-none">
                        @Model.Article.Category.Name
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    @Model.Article.Title
                </li>
            </ol>
        </div>
    </nav>

    <!-- Article Content -->
    <div class="container py-5">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <article class="article-detail">
                    <!-- Article Header -->
                    <header class="article-header mb-5" data-aos="fade-up">
                        <!-- Category Badge -->
                        <div class="mb-3">
                            <span class="badge <EMAIL> bg-opacity-10 <EMAIL>.<NAME_EMAIL> border-opacity-25 fs-6">
                                <i class="@Model.Article.Category.Icon me-2"></i>@Model.Article.Category.Name
                            </span>
                        </div>

                        <!-- Title -->
                        <h1 class="article-title display-5 fw-bold mb-4">
                            @Model.Article.Title
                        </h1>

                        <!-- Summary -->
                        <p class="article-summary lead text-muted mb-4">
                            @Model.Article.Summary
                        </p>

                        <!-- Meta Info -->
                        <div class="article-meta d-flex flex-wrap align-items-center gap-4 mb-4 pb-4 border-bottom">
                            <div class="author-info d-flex align-items-center">
                                @if (!string.IsNullOrEmpty(Model.Article.AuthorAvatar))
                                {
                                    <img src="@Model.Article.AuthorAvatar" 
                                         class="rounded-circle me-3" 
                                         width="48" height="48" 
                                         alt="@Model.Article.Author">
                                }
                                <div>
                                    <div class="fw-semibold">@Model.Article.Author</div>
                                    <small class="text-muted">作者</small>
                                </div>
                            </div>
                            
                            <div class="article-stats d-flex gap-4 text-muted">
                                <span>
                                    <i class="bi bi-calendar me-1"></i>
                                    @Model.Article.PublishDate.ToString("yyyy年MM月dd日")
                                </span>
                                <span>
                                    <i class="bi bi-clock me-1"></i>
                                    约@Model.Article.ReadingTime分钟阅读
                                </span>
                                <span>
                                    <i class="bi bi-eye me-1"></i>
                                    @Model.Article.ViewCount 次阅读
                                </span>
                            </div>
                        </div>

                        <!-- Tags -->
                        @if (Model.Article.Tags.Any())
                        {
                            <div class="article-tags">
                                @foreach (var tag in Model.Article.Tags)
                                {
                                    <span class="badge bg-light text-muted me-2 mb-2">
                                        #@tag.Name
                                    </span>
                                }
                            </div>
                        }
                    </header>

                    <!-- Featured Image -->
                    @if (!string.IsNullOrEmpty(Model.Article.FeaturedImage))
                    {
                        <div class="article-featured-image mb-5" data-aos="fade-up" data-aos-delay="100">
                            <img src="@Model.Article.FeaturedImage" 
                                 class="img-fluid rounded shadow-sm" 
                                 alt="@Model.Article.Title">
                        </div>
                    }

                    <!-- Article Content -->
                    <div class="article-content" data-aos="fade-up" data-aos-delay="200">
                        @Html.Raw(Model.Article.Content)
                    </div>

                    <!-- Article Footer -->
                    <footer class="article-footer mt-5 pt-4 border-top">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center text-muted">
                                    <i class="bi bi-clock-history me-2"></i>
                                    <span>最后更新：@Model.Article.UpdateDate.ToString("yyyy年MM月dd日")</span>
                                </div>
                            </div>
                            <div class="col-md-6 text-md-end mt-3 mt-md-0">
                                <div class="share-buttons">
                                    <span class="text-muted me-3">分享到：</span>
                                    <a href="#" class="btn btn-sm btn-outline-primary me-2" onclick="shareToWeChat()">
                                        <i class="bi bi-wechat"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-info me-2" onclick="shareToWeibo()">
                                        <i class="bi bi-share"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-secondary" onclick="copyLink()">
                                        <i class="bi bi-link-45deg"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </footer>
                </article>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sidebar-sticky">
                    <!-- Table of Contents -->
                    <div class="card border-0 shadow-sm mb-4" id="tocCard" style="display: none;">
                        <div class="card-header bg-white border-0 py-3">
                            <h5 class="mb-0 fw-bold">
                                <i class="bi bi-list-ul me-2"></i>目录
                            </h5>
                        </div>
                        <div class="card-body">
                            <nav id="tableOfContents" class="toc-nav">
                                <!-- TOC will be generated by JavaScript -->
                            </nav>
                        </div>
                    </div>

                    <!-- Related Articles -->
                    @if (Model.Article.RelatedArticles.Any())
                    {
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-white border-0 py-3">
                                <h5 class="mb-0 fw-bold">
                                    <i class="bi bi-bookmark me-2"></i>相关文章
                                </h5>
                            </div>
                            <div class="card-body">
                                @foreach (var relatedArticle in Model.Article.RelatedArticles)
                                {
                                    <div class="related-article-item mb-3 pb-3 @(relatedArticle != Model.Article.RelatedArticles.Last() ? "border-bottom" : "")">
                                        <h6 class="mb-2">
                                            <a asp-page="/ArticleDetail" 
                                               asp-route-slug="@relatedArticle.Slug" 
                                               class="text-decoration-none">
                                                @relatedArticle.Title
                                            </a>
                                        </h6>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <span class="badge <EMAIL> bg-opacity-10 <EMAIL>">
                                                    @relatedArticle.CategoryName
                                                </span>
                                            </small>
                                            <small class="text-muted">
                                                <i class="bi bi-eye me-1"></i>@relatedArticle.ViewCount
                                            </small>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <!-- Categories Navigation -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-0 py-3">
                            <h5 class="mb-0 fw-bold">
                                <i class="bi bi-folder me-2"></i>文章分类
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                @foreach (var category in Model.Categories)
                                {
                                    <a asp-page="/Articles" 
                                       asp-route-category="@category.Id"
                                       class="list-group-item list-group-item-action border-0 @(Model.Article.Category.Id == category.Id ? "active" : "")">
                                        <i class="@category.Icon me-2"></i>@category.Name
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <!-- Article Not Found -->
    <div class="container py-5">
        <div class="text-center">
            <div class="mb-4">
                <i class="bi bi-journal-x display-1 text-muted"></i>
            </div>
            <h1 class="h3 text-muted mb-3">文章未找到</h1>
            <p class="text-muted mb-4">抱歉，您访问的文章不存在或已被删除。</p>
            <a asp-page="/Articles" class="btn btn-primary">
                <i class="bi bi-arrow-left me-2"></i>返回文章列表
            </a>
        </div>
    </div>
}
