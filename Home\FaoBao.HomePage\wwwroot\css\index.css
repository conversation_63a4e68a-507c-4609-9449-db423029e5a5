/*--------------------------------------------------------------
# Professional Hero Carousel Section
--------------------------------------------------------------*/
.hero {
  width: 100%;
  min-height: 50vh;
  position: relative;
  padding: 0;
  overflow: hidden;
}

.hero-carousel {
  height: 50vh;
}

.hero-slide {
  height: 50vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* 简化的背景设计 */
.hero-slide-1 {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: var(--heading-color);
}

.hero-slide-2 {
  background: linear-gradient(135deg, #2f70f5 0%, #1d4ed8 100%);
  color: white;
}

.hero-slide-3 {
  background: linear-gradient(135deg, #1e40af 0%, #2f70f5 100%);
  color: white;
}

/* 简化装饰图案 */
.hero-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(47, 112, 245, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(47, 112, 245, 0.05) 0%,
      transparent 50%
    );
  z-index: 1;
}

.hero-slide .container {
  position: relative;
  z-index: 2;
}

.hero-content {
  text-align: left;
}

/* 简化徽章设计 */
.hero-badge {
  display: inline-block;
  padding: 6px 12px;
  background: var(--primary-blue);
  color: white;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  letter-spacing: 0.3px;
}

.hero-slide-2 .hero-badge,
.hero-slide-3 .hero-badge {
  background: rgba(255, 255, 255, 0.25);
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.hero-subtitle {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  max-width: 450px;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.hero-buttons .btn {
  padding: 12px 24px;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  font-size: 0.95rem;
}

.hero-buttons .btn-primary {
  background: var(--primary-blue);
  border: 2px solid var(--primary-blue);
  color: white;
}

.hero-buttons .btn-primary:hover {
  background: var(--primary-blue-hover);
  border-color: var(--primary-blue-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(47, 112, 245, 0.3);
}

.hero-buttons .btn-outline-dark {
  border: 2px solid var(--heading-color);
  color: var(--heading-color);
  background: transparent;
}

.hero-buttons .btn-outline-dark:hover {
  background: var(--heading-color);
  color: white;
  transform: translateY(-1px);
}

/* Hero统计数据 */
.hero-stats-inline {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
  color: var(--primary-blue);
}

.stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
  font-weight: 500;
}

/* Hero特性列表 */
.hero-features {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.feature-item i {
  font-size: 1rem;
  opacity: 0.9;
  color: var(--primary-blue);
}

/* Hero行业标签 */
.hero-industries {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.industry-tag {
  padding: 4px 10px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* 简化视觉元素 */
.hero-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.hero-visual {
  position: relative;
  width: 100%;
  max-width: 350px;
  height: 250px;
}

/* 简化业务卡片 */
.business-card {
  position: absolute;
  background: white;
  border: 1px solid rgba(47, 112, 245, 0.15);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 12px rgba(47, 112, 245, 0.08);
  width: 140px;
  height: 90px;
  transition: transform 0.2s ease;
}

.business-card:hover {
  transform: translateY(-2px);
}

.business-card.card-2 {
  top: 90px;
  right: 0;
  background: var(--primary-blue);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0.8;
}

.card-trend {
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--primary-blue);
}

.business-card.card-2 .card-trend {
  color: #60a5fa;
}

.card-chart {
  height: 40px;
  background: rgba(47, 112, 245, 0.1);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.card-chart::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  background: var(--primary-blue);
  border-radius: 0 0 4px 4px;
}

/* 简化技术网格 */
.tech-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  max-width: 260px;
}

.tech-item {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: opacity 0.2s ease;
}

.tech-item:hover {
  opacity: 0.9;
}

.tech-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  font-size: 1rem;
}

.tech-label {
  font-size: 0.75rem;
  font-weight: 600;
  opacity: 0.95;
}

/* 解决方案图表 */
.solution-diagram {
  position: relative;
  width: 280px;
  height: 280px;
  margin: 0 auto;
}

.solution-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.center-icon {
  font-size: 1.2rem;
  margin-bottom: 2px;
}

.center-label {
  font-size: 0.65rem;
  font-weight: 600;
  opacity: 0.9;
}

.solution-nodes {
  position: relative;
  width: 100%;
  height: 100%;
}

.node {
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s ease;
}

.node:hover {
  opacity: 0.9;
}

.node i {
  font-size: 1rem;
  margin-bottom: 2px;
}

.node span {
  font-size: 0.6rem;
  font-weight: 600;
  text-align: center;
  opacity: 0.9;
}

.node-1 {
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
}

.node-2 {
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
}

.node-3 {
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
}

.node-4 {
  top: 50%;
  left: 15px;
  transform: translateY(-50%);
}

/* 统计卡片样式 */
.hero-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 24px 32px;
  text-align: center;
  color: white;
  min-width: 180px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.95rem;
  opacity: 0.9;
}

/* 仪表板样式 */
.hero-dashboard {
  position: relative;
  width: 350px;
  height: 250px;
}

.dashboard-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 200px;
  height: 120px;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-placeholder::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 简化轮播图控制器 */
.carousel-indicators {
  bottom: 20px;
  margin-bottom: 0;
}

.carousel-indicators [data-bs-target] {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 6px;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  transition: all 0.2s ease;
}

.carousel-indicators .active {
  background-color: white;
  width: 20px;
  border-radius: 4px;
}

.carousel-control-prev,
.carousel-control-next {
  width: 40px;
  height: 40px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  border: none;
  transition: opacity 0.2s ease;
  opacity: 0.6;
}

.carousel-control-prev {
  left: 20px;
}

.carousel-control-next {
  right: 20px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 16px;
  height: 16px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .hero {
    min-height: 60vh;
  }

  .hero-carousel {
    height: 60vh;
  }

  .hero-slide {
    height: 60vh;
  }

  .hero-title {
    font-size: 1.8rem;
    margin-bottom: 0.75rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .hero-buttons {
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .hero-buttons .btn {
    min-width: 100px;
    padding: 8px 16px;
    font-size: 0.85rem;
  }

  .hero-badge {
    font-size: 0.7rem;
    padding: 4px 8px;
    margin-bottom: 0.75rem;
  }

  /* 移动端隐藏复杂视觉元素 */
  .hero-visual {
    display: none;
  }

  .hero-stats-inline {
    gap: 1rem;
    justify-content: center;
    margin-top: 0.75rem;
  }

  .stat-number {
    font-size: 1.25rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .hero-features {
    gap: 1rem;
    justify-content: center;
    margin-top: 0.75rem;
  }

  .feature-item {
    font-size: 0.8rem;
  }

  .hero-industries {
    justify-content: center;
    margin-top: 0.75rem;
  }

  .industry-tag {
    font-size: 0.7rem;
    padding: 3px 8px;
  }

  .carousel-control-prev,
  .carousel-control-next {
    width: 32px;
    height: 32px;
  }

  .carousel-control-prev {
    left: 10px;
  }

  .carousel-control-next {
    right: 10px;
  }

  .carousel-indicators {
    bottom: 15px;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .hero {
    min-height: 70vh;
  }

  .hero-carousel {
    height: 70vh;
  }

  .hero-slide {
    height: 70vh;
  }

  .hero-title {
    font-size: 1.5rem;
    line-height: 1.3;
  }

  .hero-subtitle {
    font-size: 0.85rem;
    line-height: 1.5;
  }

  .hero-buttons .btn {
    min-width: 90px;
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .hero-stats-inline {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .stat-item {
    align-items: center;
    text-align: center;
  }

  .hero-features {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .hero-industries {
    gap: 0.25rem;
  }

  .industry-tag {
    font-size: 0.65rem;
    padding: 2px 6px;
  }
}

/*--------------------------------------------------------------
# Advantage Section
--------------------------------------------------------------*/
.advantage {
  background: var(--light-bg);
  position: relative;
}

.advantage::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.8" fill="rgba(43,108,176,0.06)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.4;
}

/* 强制应用Bootstrap间距 */
.advantage .row.g-4 {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 1.5rem;
  margin-left: calc(-0.5 * var(--bs-gutter-x));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
}

.advantage .row.g-4 > * {
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  margin-bottom: var(--bs-gutter-y);
}

/* 额外的间距确保 */
@media (min-width: 992px) {
  .advantage .col-lg-6 {
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-bottom: 30px !important;
  }
}

/* 强制间距 - 使用margin方式 */
.advantage .icon-box {
  margin-left: 15px !important;
  margin-right: 15px !important;
  margin-bottom: 30px !important;
}

/* 企业级核心概况区域 */
.advantage {
  background: #ffffff;
  position: relative;
  padding: 100px 0;
}

.advantage::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="advantage-dots" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(47,112,245,0.06)"/></pattern></defs><rect width="100" height="100" fill="url(%23advantage-dots)"/></svg>');
  opacity: 0.6;
}

.advantage .section-title {
  text-align: center;
  padding-bottom: 80px;
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  z-index: 2;
}

.advantage .section-title h2 {
  color: var(--heading-color);
  font-size: 2.75rem;
  font-weight: 700;
  position: relative;
  margin-bottom: 24px;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.advantage .section-title h2::after {
  content: "";
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--primary-blue);
  border-radius: 2px;
}

.advantage .section-title p {
  margin-bottom: 0;
  font-size: 1.125rem;
  color: var(--muted-color);
  line-height: 1.7;
  margin-top: 20px;
  font-weight: 400;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.advantage .content h3 {
  font-weight: 700;
  font-size: 2.25rem;
  margin-bottom: 24px;
  color: var(--heading-color);
  line-height: 1.3;
}

.advantage .content p {
  margin-bottom: 32px;
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--muted-color);
}

.advantage .icon-box {
  background: #ffffff;
  padding: 28px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.advantage .icon-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-blue);
  transform: scaleX(0);
  transition: transform 0.2s ease;
}

.advantage .icon-box:hover::before {
  transform: scaleX(1);
}

.advantage .icon-box:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-blue);
}

.advantage .icon-box i {
  font-size: 2.5rem;
  color: var(--primary-blue);
  margin-bottom: 16px;
  margin-right: 0;
  display: block;
  text-align: center;
  transition: all 0.2s ease;
}

.advantage .icon-box:hover i {
  color: var(--primary-blue-hover);
  transform: scale(1.05);
}

.advantage .icon-box h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.advantage .icon-box h4 a {
  color: var(--heading-color);
  transition: all 0.3s ease;
  text-decoration: none;
}

.advantage .icon-box p {
  font-size: 0.95rem;
  color: var(--muted-color);
  margin-bottom: 0;
  line-height: 1.6;
}

.advantage .icon-box:hover h4 a {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .advantage .content h3 {
    font-size: 1.875rem;
    text-align: center;
    margin-bottom: 20px;
  }

  .advantage .content p {
    margin-bottom: 24px;
    font-size: 1rem;
    text-align: center;
  }

  .advantage .icon-box {
    padding: 24px 20px;
    margin-bottom: 20px;
  }
}

/*--------------------------------------------------------------
# Clients Section
--------------------------------------------------------------*/
.clients {
  padding: 30px 0 30px 0;
}
.clients .swiper {
  padding: 10px 0;
}

.clients .swiper-wrapper {
  height: auto;
}

.clients .swiper-slide img {
  transition: 0.3s;
}

.clients .swiper-slide img:hover {
  transform: scale(1.1);
}
@media (max-width: 991.98px) {
  .clients {
    padding: 0px !important;
  }
  .clients .container {
    padding: 0px !important;
  }
}
/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services {
  background: var(--light-bg);
  position: relative;
}

.services::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(43,108,176,0.08)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.4;
}

.services .container {
  position: relative;
  z-index: 1;
}

/* 强制应用Bootstrap间距 - Services部分 */
.services .row.g-4 {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 1.5rem;
  margin-left: calc(-0.5 * var(--bs-gutter-x));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
}

.services .row.g-4 > * {
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  margin-bottom: var(--bs-gutter-y);
}

/* 额外的间距确保 - Services部分 */
@media (min-width: 992px) {
  .services .col-lg-4 {
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-bottom: 30px !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .services .col-md-6 {
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-bottom: 30px !important;
  }
}

/* 强制间距 - 使用margin方式 */
.services .service-item {
  margin-left: 15px !important;
  margin-right: 15px !important;
  margin-bottom: 0px !important;
}

/* 使用padding方式确保间距 */
.services .col-lg-4,
.services .col-md-6 {
  padding: 15px !important;
}

.advantage .col-lg-6 {
  padding: 15px !important;
}

.services .service-item {
  position: relative;
  height: 100%;
  background: #ffffff;
  padding: 32px 24px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 24px;
}

.services .service-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--accent-color);
  transform: scaleX(0);
  transition: transform 0.2s ease;
}

.services .service-item:hover::before {
  transform: scaleX(1);
}

.services .service-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
  border-color: var(--accent-color);
}

.services .service-item .icon {
  margin-right: 0;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: rgba(43, 108, 176, 0.1);
  transition: all 0.2s ease;
}

.services .service-item .icon i {
  color: var(--accent-color);
  font-size: 28px;
  line-height: 0;
  transition: all 0.3s ease;
}

.services .service-item:hover .icon {
  background: var(--accent-color);
  transform: scale(1.05);
}

.services .service-item:hover .icon i {
  color: #ffffff;
  transform: scale(1.1);
}

.services .service-item .title {
  color: var(--heading-color);
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 1.25rem;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.services .service-item .description {
  font-size: 0.95rem;
  color: var(--muted-color);
  margin-bottom: 20px;
  line-height: 1.6;
}

.services .service-item .readmore {
  display: inline-flex;
  align-items: center;
  color: var(--accent-color);
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.875rem;
  text-decoration: none;
  padding: 8px 0;
  border-bottom: 1px solid transparent;
}

.services .service-item .readmore i {
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.services .service-item:hover .title {
  color: var(--accent-color);
}

.services .service-item:hover .readmore {
  border-bottom-color: var(--accent-color);
}

.services .service-item:hover .readmore i {
  transform: translateX(4px);
}

@media (max-width: 768px) {
  .services .service-item {
    padding: 32px 24px;
    margin-bottom: 24px;
  }

  .services .service-item .icon {
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
  }

  .services .service-item .icon i {
    font-size: 28px;
  }

  .services .service-item .title {
    font-size: 1.125rem;
    margin-bottom: 12px;
  }
}

/*--------------------------------------------------------------
# Call To Action Section
--------------------------------------------------------------*/
.call-to-action {
  --background-color: none;
}

.call-to-action .container {
  padding-top: 80px;
  padding-bottom: 80px;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  clip-path: inset(0 round 15px);
}

.call-to-action .container img {
  position: fixed;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  border-radius: 15px;
  overflow: hidden;
}

.call-to-action .container:before {
  content: "";
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  inset: 0;
  z-index: 2;
}

.call-to-action .container .content {
  position: relative;
  z-index: 3;
}

.call-to-action p {
  color: var(--default-color);
  margin-bottom: 20px;
  margin-top: 20px;
}

.call-to-action .pulsating-play-btn {
  display: inline-block;
}

.call-to-action .cta-btn {
  font-family: var(--heading-font);
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 1px;
  display: inline-block;
  padding: 12px 40px;
  border-radius: 5px;
  transition: 0.5s;
  margin: 10px;
  border: 2px solid var(--contrast-color);
  color: var(--contrast-color);
}

.call-to-action .cta-btn:hover {
  background: var(--accent-color);
  border: 2px solid var(--accent-color);
}

/*--------------------------------------------------------------
# Faq Section
--------------------------------------------------------------*/
.faq .faq-container .faq-item {
  position: relative;
  padding: 20px 0;
  border-bottom: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
  overflow: hidden;
}

.faq .faq-container .faq-item:last-child {
  margin-bottom: 0;
}

.faq .faq-container .faq-item h3 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  margin: 0 30px 0 0;
  transition: 0.3s;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.faq .faq-container .faq-item h3 .num {
  color: var(--accent-color);
  padding-right: 5px;
}

.faq .faq-container .faq-item h3:hover {
  color: var(--accent-color);
}

.faq .faq-container .faq-item .faq-content {
  display: grid;
  grid-template-rows: 0fr;
  transition: 0.3s ease-in-out;
  visibility: hidden;
  opacity: 0;
}

.faq .faq-container .faq-item .faq-content p {
  margin-bottom: 0;
  overflow: hidden;
}

.faq .faq-container .faq-item .faq-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 16px;
  line-height: 0;
  transition: 0.3s;
  cursor: pointer;
}

.faq .faq-container .faq-item .faq-toggle:hover {
  color: var(--accent-color);
}

.faq .faq-container .faq-active h3 {
  color: var(--accent-color);
}

.faq .faq-container .faq-active .faq-content {
  grid-template-rows: 1fr;
  visibility: visible;
  opacity: 1;
  padding-top: 10px;
}

.faq .faq-container .faq-active .faq-toggle {
  transform: rotate(90deg);
  color: var(--accent-color);
}

.category {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 40px 32px;
  background: #ffffff;
  border-radius: 28px;
  text-decoration: none !important;
  border: 1px solid rgba(0, 119, 255, 0.1);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  min-height: 280px;
  position: relative;
  overflow: hidden;
  color: inherit;
}

.category::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.category:hover::before {
  transform: scaleX(1);
}

.category:hover {
  transform: translateY(-16px);
  box-shadow: 0 32px 64px rgba(59, 130, 246, 0.25);
  border-color: var(--accent-color);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/*--------------------------------------------------------------
# Scene Section - Modern Design
--------------------------------------------------------------*/
.scene {
  background: #ffffff;
  position: relative;
  padding: 80px 0;
  overflow: hidden;
}

.scene::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.8" fill="rgba(43,108,176,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.scene .container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
}

/* Scene Row Layout - Fixed Grid */
.scene .row {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 24px !important;
  width: 100% !important;
  margin: 0 !important;
}

.scene .row > div {
  display: flex !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
}

.scene .row > div .category {
  width: 100% !important;
}

.scene .section-title h2 {
  color: var(--heading-color) !important;
}

.scene .section-title p {
  color: var(--default-color) !important;
}

/* Professional Scene Cards */
.scene .category {
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 28px 20px;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 240px !important;
  min-height: 240px !important;
  max-height: 240px !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-decoration: none;
}

.scene .category::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--accent-color);
  transform: scaleX(0);
  transition: transform 0.2s ease;
}

.scene .category:hover::before {
  transform: scaleX(1);
}

.scene .category:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color);
}

/* Professional Scene Icons */
.scene .category i,
.scene .category .bi,
section.scene .category i,
section.scene .category .bi,
section#scene .category i,
section#scene .category .bi {
  width: 64px !important;
  height: 64px !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 28px !important;
  color: #ffffff !important;
  margin-bottom: 16px !important;
  margin-top: 0 !important;
  position: relative !important;
  transition: all 0.2s ease !important;
  background: var(--accent-color) !important;
  box-shadow: 0 4px 12px rgba(43, 108, 176, 0.2) !important;
  flex-shrink: 0 !important;
  min-width: 64px !important;
  min-height: 64px !important;
  max-width: 64px !important;
  max-height: 64px !important;
  line-height: 1 !important;
}

.scene .category:hover i {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(43, 108, 176, 0.3);
}

/* Scene Text */
.scene .category h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.scene .category span {
  font-size: 0.95rem;
  color: var(--muted-color);
  line-height: 1.5;
  font-weight: 500;
}

.scene .category:hover h3 {
  color: var(--accent-color);
}

.scene .category:hover span {
  color: var(--default-color);
}

/* Professional Color Themes for Scene Icons */
.scene .category[data-theme="purple"] i {
  background: #8b5cf6 !important;
}

.scene .category[data-theme="pink"] i {
  background: #ec4899 !important;
}

.scene .category[data-theme="orange"] i {
  background: #f97316 !important;
}

.scene .category[data-theme="green"] i {
  background: #10b981 !important;
}

.scene .category[data-theme="blue"] i {
  background: #3b82f6 !important;
}

.scene .category[data-theme="indigo"] i {
  background: #6366f1 !important;
}

.scene .category[data-theme="teal"] i {
  background: #14b8a6 !important;
}

.scene .category[data-theme="gray"] i {
  background: #6b7280 !important;
}

/* Scene Responsive Grid */
@media (max-width: 1200px) {
  .scene .container {
    max-width: 960px;
  }
}

@media (max-width: 992px) {
  .scene .container {
    max-width: 720px;
  }

  .scene .row {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 20px !important;
  }
}

@media (max-width: 768px) {
  .scene {
    padding: 60px 0;
  }

  .scene .container {
    max-width: 540px;
  }

  .scene .row {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 16px !important;
  }

  .scene .category {
    padding: 28px 18px;
    height: 220px !important;
    min-height: 220px !important;
    max-height: 220px !important;
    margin-bottom: 20px;
  }

  .scene .category i,
  .scene .category .bi,
  section.scene .category i,
  section.scene .category .bi,
  section#scene .category i,
  section#scene .category .bi {
    width: 64px !important;
    height: 64px !important;
    font-size: 30px !important;
    margin-bottom: 16px !important;
    border-radius: 16px !important;
    min-width: 64px !important;
    min-height: 64px !important;
    max-width: 64px !important;
    max-height: 64px !important;
  }

  .scene .category h3 {
    font-size: 1.1rem;
    margin-bottom: 10px;
  }

  .scene .category span {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .scene .container {
    max-width: 100%;
    padding: 0 15px;
  }

  .scene .row {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }

  .scene .category {
    padding: 24px 16px;
    height: 200px !important;
    min-height: 200px !important;
    max-height: 200px !important;
  }

  .scene .category i,
  .scene .category .bi,
  section.scene .category i,
  section.scene .category .bi,
  section#scene .category i,
  section#scene .category .bi {
    width: 56px !important;
    height: 56px !important;
    font-size: 26px !important;
    margin-bottom: 14px !important;
    border-radius: 14px !important;
    min-width: 56px !important;
    min-height: 56px !important;
    max-width: 56px !important;
    max-height: 56px !important;
  }

  .scene .category h3 {
    font-size: 1rem;
    margin-bottom: 8px;
  }

  .scene .category span {
    font-size: 0.85rem;
  }
}

/* Scene Footer */
.scene-footer {
  margin-top: 60px;
}

.scene-footer p {
  color: var(--default-color) !important;
  font-size: 1.1rem;
  margin-bottom: 40px;
}

.scene-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  color: var(--heading-color);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  display: block;
  font-size: 0.95rem;
  color: var(--muted-color);
  font-weight: 500;
}

@media (max-width: 768px) {
  .scene-footer {
    margin-top: 40px;
  }

  .scene-stats {
    gap: 40px;
  }

  .stat-number {
    font-size: 2rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .scene-stats {
    gap: 30px;
  }

  .stat-number {
    font-size: 1.75rem;
  }
}

/* Professional hover effects for different themes */
.category[data-theme="purple"]:hover {
  border-color: #8b5cf6;
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.15);
}

.category[data-theme="pink"]:hover {
  border-color: #ec4899;
  box-shadow: 0 8px 20px rgba(236, 72, 153, 0.15);
}

.category[data-theme="orange"]:hover {
  border-color: #f97316;
  box-shadow: 0 8px 20px rgba(249, 115, 22, 0.15);
}

.category[data-theme="green"]:hover {
  border-color: #10b981;
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.15);
}

.category[data-theme="blue"]:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.15);
}

.category[data-theme="indigo"]:hover {
  border-color: #6366f1;
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.15);
}

.category[data-theme="teal"]:hover {
  border-color: #14b8a6;
  box-shadow: 0 8px 20px rgba(20, 184, 166, 0.15);
}

.category[data-theme="gray"]:hover {
  border-color: #6b7280;
  box-shadow: 0 8px 20px rgba(107, 114, 128, 0.15);
}

.category h3 {
  margin: 0 0 12px 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--heading-color);
  transition: all 0.2s ease;
}

.category span {
  font-size: 0.95rem;
  color: var(--muted-color);
  line-height: 1.5;
  transition: all 0.2s ease;
  font-weight: 400;
}

.category:hover h3 {
  color: var(--accent-color);
}

.category:hover span {
  color: var(--default-color);
}
