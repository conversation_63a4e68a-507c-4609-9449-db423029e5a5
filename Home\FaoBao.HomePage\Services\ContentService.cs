﻿using FaoBao.HomePage.Models;
using System.Text.Json;

namespace FaoBao.HomePage.Services
{
    /// <summary>
    /// 基于JSON文件的内容服务实现
    /// </summary>
    public class ContentService
    {
        private readonly ILogger<ContentService> _logger;
        private readonly IWebHostEnvironment _environment;
        private readonly JsonSerializerOptions _jsonOptions;

        // 缓存数据
        private HomePageData? _homePageData;
        private ProductPageData? _productPageData;
        private DocumentCategoriesData? _documentCategoriesPageData;
        private ArticleData? _articleData;

        public ContentService(ILogger<ContentService> logger, IWebHostEnvironment environment)
        {
            _logger = logger;
            _environment = environment;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        public async Task<HomePageData> GetHomePageDataAsync()
        {
            // 如果缓存有数据，直接返回
            if (_homePageData != null)
                return _homePageData;
            try
            {
                _homePageData = await GetData<HomePageData>("homepage.json");
                return _homePageData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading homepage.json file");
                throw;
            }
        }

        public async Task<ProductPageData> GetProductPageDataAsync()
        {
            // 如果缓存有数据，直接返回
            if (_productPageData != null)
            {
                return _productPageData;
            }

            try
            {
                _productPageData = await GetData<ProductPageData>("products.json");
                return _productPageData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading products.json file");
                throw;
            }
        }

        public async Task<DocumentCategoriesData> GetDocumentCategoriesDataAsync()
        {
            // 如果缓存有数据，直接返回
            if (_documentCategoriesPageData != null)
            {
                return _documentCategoriesPageData;
            }

            try
            {
                _documentCategoriesPageData= await GetData<DocumentCategoriesData>("document.json");
              
                return _documentCategoriesPageData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading help.json file");
                throw;
            }
        }
        
        

        // 文章相关方法实现
        public async Task<ArticlePageData> GetArticlePageDataAsync(int page = 1, int pageSize = 12,
            string? categoryId = null, string? keyword = null)
        {
            var articleData = await GetArticleDataAsync();
            var articles = articleData.Articles.Where(a => a.IsPublished).ToList();

            // 分类过滤
            if (!string.IsNullOrEmpty(categoryId))
            {
                articles = articles.Where(a => a.CategoryId == categoryId).ToList();
            }

            // 关键词搜索
            if (!string.IsNullOrEmpty(keyword))
            {
                articles = articles.Where(a =>
                    a.Title.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                    a.Summary.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                    a.Content.Contains(keyword, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }

            // 排序
            articles = articles.OrderByDescending(a => a.IsFeatured)
                .ThenByDescending(a => a.PublishDate)
                .ThenBy(a => a.Order)
                .ToList();

            // 分页
            var totalCount = articles.Count;
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            var pagedArticles = articles.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            // 转换为列表项
            var articleListItems = await ConvertToArticleListItemsAsync(pagedArticles);
            var featuredArticles = await GetFeaturedArticlesAsync(6);

            return new ArticlePageData
            {
                Categories = articleData.Categories.Where(c => c.IsActive).OrderBy(c => c.Order).ToList(),
                Tags = articleData.Tags.Where(t => t.IsActive).ToList(),
                Articles = articleListItems,
                FeaturedArticles = featuredArticles,
                TotalCount = totalCount,
                PageSize = pageSize,
                CurrentPage = page,
                TotalPages = totalPages
            };
        }

        public async Task<ArticleDetail?> GetArticleDetailAsync(string slug)
        {
            var articleData = await GetArticleDataAsync();
            var article = articleData.Articles.FirstOrDefault(a => a.Slug == slug && a.IsPublished);

            if (article == null)
                return null;

            var category = articleData.Categories.FirstOrDefault(c => c.Id == article.CategoryId) ??
                           new ArticleCategory();
            var tags = articleData.Tags.Where(t => article.TagIds.Contains(t.Id)).ToList();
            var relatedArticles = await GetRelatedArticlesAsync(article.Id, 4);

            return new ArticleDetail
            {
                Id = article.Id,
                Title = article.Title,
                Slug = article.Slug,
                Summary = article.Summary,
                Content = article.Content,
                FeaturedImage = article.FeaturedImage,
                Author = article.Author,
                AuthorAvatar = article.AuthorAvatar,
                Category = category,
                Tags = tags,
                PublishDate = article.PublishDate,
                UpdateDate = article.UpdateDate,
                ViewCount = article.ViewCount,
                ReadingTime = article.ReadingTime,
                MetaDescription = article.MetaDescription,
                MetaKeywords = article.MetaKeywords,
                RelatedArticles = relatedArticles
            };
        }

        public async Task<List<ArticleListItem>> GetFeaturedArticlesAsync(int count = 6)
        {
            var articleData = await GetArticleDataAsync();
            var featuredArticles = articleData.Articles
                .Where(a => a.IsPublished && a.IsFeatured)
                .OrderByDescending(a => a.PublishDate)
                .Take(count)
                .ToList();

            return await ConvertToArticleListItemsAsync(featuredArticles);
        }

        public async Task<List<ArticleListItem>> GetRelatedArticlesAsync(string articleId, int count = 4)
        {
            var articleData = await GetArticleDataAsync();
            var currentArticle = articleData.Articles.FirstOrDefault(a => a.Id == articleId);

            if (currentArticle == null)
                return new List<ArticleListItem>();

            var relatedArticles = articleData.Articles
                .Where(a => a.IsPublished && a.Id != articleId)
                .Where(a => a.CategoryId == currentArticle.CategoryId ||
                            a.TagIds.Any(t => currentArticle.TagIds.Contains(t)))
                .OrderByDescending(a => a.PublishDate)
                .Take(count)
                .ToList();

            return await ConvertToArticleListItemsAsync(relatedArticles);
        }

        public async Task<ArticleSearchResult> SearchArticlesAsync(string keyword, string? categoryId = null,
            List<string>? tagIds = null)
        {
            var articleData = await GetArticleDataAsync();
            var articles = articleData.Articles.Where(a => a.IsPublished).ToList();

            // 关键词搜索
            if (!string.IsNullOrEmpty(keyword))
            {
                articles = articles.Where(a =>
                    a.Title.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                    a.Summary.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                    a.Content.Contains(keyword, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }

            // 分类过滤
            if (!string.IsNullOrEmpty(categoryId))
            {
                articles = articles.Where(a => a.CategoryId == categoryId).ToList();
            }

            // 标签过滤
            if (tagIds != null && tagIds.Any())
            {
                articles = articles.Where(a => a.TagIds.Any(t => tagIds.Contains(t))).ToList();
            }

            var articleListItems = await ConvertToArticleListItemsAsync(articles);

            return new ArticleSearchResult
            {
                Articles = articleListItems,
                Keyword = keyword ?? string.Empty,
                CategoryId = categoryId ?? string.Empty,
                TagIds = tagIds ?? new List<string>(),
                TotalCount = articles.Count
            };
        }

        public async Task<List<ArticleCategory>> GetArticleCategoriesAsync()
        {
            var articleData = await GetArticleDataAsync();
            return articleData.Categories.Where(c => c.IsActive).OrderBy(c => c.Order).ToList();
        }


        private async Task<ArticleData> GetArticleDataAsync()
        {
            // 如果缓存有数据，直接返回
            if (_articleData != null)
            {
                return _articleData;
            }

            try
            {
                var filePath = Path.Combine(_environment.ContentRootPath, "Data", "articles_new.json");
                var jsonContent = await File.ReadAllTextAsync(filePath);
                _articleData = JsonSerializer.Deserialize<ArticleData>(jsonContent, _jsonOptions);

                if (_articleData == null)
                {
                    _logger.LogError("Failed to deserialize articles.json");
                    throw new InvalidOperationException("Failed to deserialize articles.json");
                }

                return _articleData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading articles.json file");
                throw;
            }
        }

        private async Task<List<ArticleListItem>> ConvertToArticleListItemsAsync(List<Article> articles)
        {
            var articleData = await GetArticleDataAsync();
            var result = new List<ArticleListItem>();

            foreach (var article in articles)
            {
                var category = articleData.Categories.FirstOrDefault(c => c.Id == article.CategoryId);
                var tags = articleData.Tags.Where(t => article.TagIds.Contains(t.Id)).ToList();

                result.Add(new ArticleListItem
                {
                    Id = article.Id,
                    Title = article.Title,
                    Slug = article.Slug,
                    Summary = article.Summary,
                    FeaturedImage = article.FeaturedImage,
                    Author = article.Author,
                    AuthorAvatar = article.AuthorAvatar,
                    CategoryName = category?.Name ?? "",
                    CategoryColor = category?.Color ?? "primary",
                    Tags = tags,
                    PublishDate = article.PublishDate,
                    ViewCount = article.ViewCount,
                    ReadingTime = article.ReadingTime,
                    IsFeatured = article.IsFeatured
                });
            }

            return result;
        }

        private async Task<T> GetData<T>(string fileName)
        {
            try
            {
                var filePath = Path.Combine(_environment.ContentRootPath, "Data", fileName);
                var jsonContent = await File.ReadAllTextAsync(filePath);
                var result = JsonSerializer.Deserialize<T>(jsonContent, _jsonOptions);

                if (result != null) return result;
                _logger.LogError("Failed to deserialize homepage.json");
                throw new InvalidOperationException("Failed to deserialize homepage.json");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading homepage.json file");
                throw;
            }
        }
    }

    /// <summary>
    /// 帮助文档JSON数据结构
    /// </summary>
    public class HelpData
    {
        public List<HelpCategory> Categories { get; set; } = new();
    }
}