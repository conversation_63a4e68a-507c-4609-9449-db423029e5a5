﻿namespace FaoBao.HomePage.Models
{
    /// <summary>
    /// 首页优势项目
    /// </summary>
    public class AdvantageItem
    {
        public int Id { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Order { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 服务项目
    /// </summary>
    public class ServiceItem
    {
        public int Id { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ReadMoreUrl { get; set; } = string.Empty;
        public int Order { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 应用场景分类
    /// </summary>
    public class SceneCategory
    {
        public int Id { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string ColorTheme { get; set; } = "blue";
        public int Order { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// FAQ项目
    /// </summary>
    public class FaqItem
    {
        public int Id { get; set; }
        public string Question { get; set; } = string.Empty;
        public string Answer { get; set; } = string.Empty;
        public int Order { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 产品信息
    /// </summary>
    public class Product
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Logo { get; set; } = string.Empty;
        public string LogoClass { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string CategoryDisplay { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> Features { get; set; } = new();
        public ProductStats Stats { get; set; } = new();
        public bool IsPopular { get; set; }
        public bool IsActive { get; set; } = true;
        public int Order { get; set; }
    }

    /// <summary>
    /// 产品统计信息
    /// </summary>
    public class ProductStats
    {
        public string Users { get; set; } = string.Empty;
        public string Orders { get; set; } = string.Empty;
        public string SuccessRate { get; set; } = string.Empty;
    }

    /// <summary>
    /// 帮助文档分类
    /// </summary>
    public class HelpCategory
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public List<HelpDocument> Documents { get; set; } = new();
        public int Order { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 帮助文档
    /// </summary>
    public class HelpDocument
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime UpdateTime { get; set; }
        public string CategoryId { get; set; } = string.Empty;
        public int Order { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 网站统计信息
    /// </summary>
    public class SiteStats
    {
        public string SupportedPlatforms { get; set; } = "10+";
        public string ServedMerchants { get; set; } = "100万+";
        public string SuccessRate { get; set; } = "99.9%";
    }

    /// <summary>
    /// 首页数据
    /// </summary>
    public class HomePageData
    {
        public List<AdvantageItem> Advantages { get; set; } = new();
        public List<ServiceItem> Services { get; set; } = new();
        public List<SceneCategory> Scenes { get; set; } = new();
        public List<FaqItem> Faqs { get; set; } = new();
    }

    /// <summary>
    /// 产品页面数据
    /// </summary>
    public class ProductPageData
    {
        public SiteStats Stats { get; set; } = new();
        public List<Product> Products { get; set; } = new();
        public List<string> Categories { get; set; } = new();
    }

    /// <summary>
    /// 帮助页面数据
    /// </summary>
    public class HelpPageData
    {
        public List<HelpCategory> Categories { get; set; } = new();
    }
}
