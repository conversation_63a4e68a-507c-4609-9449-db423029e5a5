using FaoBao.HomePage.Services;
using Microsoft.Extensions.WebEncoders;
using System.Text.Encodings.Web;
using System.Text.Unicode;   

namespace FaoBao.HomePage
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddRazorPages();
            builder.Services.AddControllers();
            builder.Services.Configure<WebEncoderOptions>(options => options.TextEncoderSettings = new TextEncoderSettings(UnicodeRanges.All));
            // 注册内容服务 - 使用JSON文件存储
            builder.Services.AddSingleton<ContentService>();
            builder.Services.AddHttpClient("yuque",
                client =>
                {
                    client.BaseAddress = new Uri("https://www.yuque.com/api/v2/");
                    client.DefaultRequestHeaders.Add("X-Auth-Token", builder.Configuration["YuQueToken"]);
                    client.DefaultRequestHeaders.Add("User-Agent","FaoBao Robot");
                });

            // 注册 OSS 服务
            builder.Services.AddScoped<OssService>();
            // 注册 Jieba 中文分词服务（单例模式，提升性能）
            builder.Services.AddSingleton<JiebaService>();
            // 注册 MeiliSearch 服务
            builder.Services.AddScoped<MeiliSearchService>();
            builder.Services.AddScoped<YuQueService>();
            builder.Services.AddScoped<DocumentService>();
            builder.Services.AddMemoryCache();
            var app = builder.Build();

            // 初始化 MeiliSearch 索引
            using (var scope = app.Services.CreateScope())
            {
                var meiliSearchService = scope.ServiceProvider.GetRequiredService<MeiliSearchService>();
                await meiliSearchService.InitializeIndexAsync();
            }

            // Configure the HTTP request pipeline.
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Error");
            }

            app.UseRouting();

            app.UseAuthorization();

            app.MapStaticAssets();
            app.MapRazorPages()
                .WithStaticAssets();
            app.MapControllers();

            app.Run();
        }
    }
}