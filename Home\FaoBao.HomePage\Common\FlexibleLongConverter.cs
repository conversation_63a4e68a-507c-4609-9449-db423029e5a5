﻿using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace FaoBao.HomePage.Common;

public class FlexibleLongConverter : JsonConverter<long?>
{
    public override long? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        switch (reader.TokenType)
        {
            case JsonTokenType.String:
            {
                var stringValue = reader.GetString();
                if (string.IsNullOrEmpty(stringValue))
                    return null;
                if (long.TryParse(stringValue, out long result))
                    return result;
                throw new JsonException($"无法将字符串 '{stringValue}' 转换为 long.");
            }
            case JsonTokenType.Number:
                return reader.GetInt64(); // 直接读取数值 
            default:
                throw new JsonException($"预期为字符串或数值，实际为 {reader.TokenType}.");
        }
    }

    public override void Write(Utf8JsonWriter writer, long? value, JsonSerializerOptions options)
    {
        if (value.HasValue)
            writer.WriteNumberValue(value.Value);
        else 
            writer.WriteNullValue();
    }

   
}